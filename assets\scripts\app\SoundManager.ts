import { _decorator, AudioClip, AudioSource, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('SoundManager')
export class SoundManager extends Component {
    private static _instance: SoundManager | null = null;

    public static get Instance(): SoundManager {
        if (!this._instance) {
            console.error("SoundManager instance is not yet available.");
        }
        return this._instance!;
    }

    protected onLoad(): void {
        SoundManager._instance = this;

        this._audioSource = this.node.addComponent(AudioSource);
        this._bgmSource = this.node.addComponent(AudioSource);
        this._bgmSource.loop = true;
    }


    @property(AudioClip)
    BGM: AudioClip = null;

    @property(AudioClip)
    SelectBolt: AudioClip = null;

    @property(AudioClip)
    NextBox: AudioClip = null;

    @property(AudioClip)
    RemoveBolt: AudioClip = null;

    @property(AudioClip)
    Knitt: AudioClip = null;

    @property(AudioClip)
    Wrap: AudioClip = null;

    @property(AudioClip)
    Complete_Level: AudioClip = null;

    @property(AudioClip)
    Lose: AudioClip = null;
    
    

    private _audioSource: AudioSource = null;
    private _bgmSource: AudioSource = null;


    public playSfx(sfxClips: AudioClip, volume: number = 1.0) {
        this._audioSource.clip = sfxClips;
        this._audioSource.volume = volume;
        this._audioSource.play();
    }

    public playBgm(bgmClips: AudioClip, volume: number = 1.0) {
        this._bgmSource.clip = bgmClips;
        this._bgmSource.volume = .8;
        this._bgmSource.play();
    }

    public stopBgm() {
        this._bgmSource.stop();
    }
    
    public pauseBgm() {
        this._bgmSource.pause();
    }
}


