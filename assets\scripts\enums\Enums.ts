export enum ObjectType {
    none = 0,
    id150 = 150,
    id511 = 511,
    id733 = 733,
    id134 = 134,
    id405 = 405,
    id672 = 672,
    id166 = 166,
    id617 = 617,
    id632 = 632,
    id507 = 507,
    id522 = 522,
    id594 = 594,
    id96 = 96,
    id489 = 489,
    id606 = 606,
    id2 = 2,
    id650 = 650,
    id295 = 295,
    id1 = 1,
    id795 = 795,
    id796 = 796,
    id749 = 749,
    id558 = 558,
    id71 = 71,
    id673 = 673,
    id679 = 679,
    id6799 = 6799,
    id752 = 752,
    id742 = 742,
    id636 = 636,
    id306 = 306,
    id520 = 520,
    id1666 = 1666,
    id29 = 29,
    id658 = 658,
    id732 = 732,
    id35 = 35,
    id797 = 797,
    id798 = 798,
    id740 = 740,
    id741 = 741,
    id729 = 729,
    id801 = 801
}

export enum ColorType {
    None = 0,
    PastelGreen = 1,
    <PERSON> = 2,
    Pink = 3,
    Yellow = 4,
    <PERSON>el<PERSON><PERSON> = 5,
    <PERSON><PERSON><PERSON> = 6,
    <PERSON> = 7,
    <PERSON><PERSON><PERSON> = 8,
    <PERSON>el<PERSON><PERSON><PERSON> = 9,
    <PERSON><PERSON><PERSON> = 10,
    <PERSON>el<PERSON><PERSON><PERSON> = 11,
    <PERSON>Gray = 12,
    Orange = 13,
    <PERSON> = 14,
    RedViolet = 15,
    BlueGreen = 16,
}