import { _decorator, Component, Node, Sprite, Color, Sprite<PERSON>rame, tween, Vec3, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('BoxLid')
export class BoxLid extends Component {

    private _images: Sprite[] = [];
    private _index: number = 0;

    public setupView(image: Sprite<PERSON>rame): void {
        this._images = this.node.children.map(child => child.getComponent(Sprite));

        for (const img of this._images) {
            img.color = new Color(255, 255, 255, 0);
            img.node.children[0].getComponent(Sprite).spriteFrame = image;
        }
    }

    public getPosPerBoxLid(): Vec3[] {
        return this._images.map(img => img.node.getWorldPosition());
    }
    public getPosPerBoxLid2(): Vec2[] {
        return this._images.map(img => new Vec2(img.node.getPosition().x, img.node.getPosition().y));
    }

    public playAnimationAppear(): void {
        const activeImages = this._images.filter(sprite => sprite.node.active);

        activeImages.forEach((sprite, effectiveIndex) => {
            tween(sprite)
                .delay(0.05 + effectiveIndex * 0.1)
                .to(0.1, { color: new Color(255, 255, 255, 255) })
                .start();
        });
    }

    public playAnimationDisappear(): void {
        const activeImages = this._images.filter(sprite => sprite.node.active);

        activeImages.reverse().forEach((sprite, effectiveIndex) => {
            tween(sprite)
                .delay(0.05 + effectiveIndex * 0.1)
                .to(0.1, { color: new Color(255, 255, 255, 0) })
                .start();
        });
    }
}


