import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('FlowUnit')
export class FlowUnit extends Component {
    @property
    layer = 0;
    @property
    _refreshOnLoad = false;
    @property
    get refreshOnLoad(){
        return this._refreshOnLoad;
    }
    set refreshOnLoad(v){
        this._refreshOnLoad = v;
    }
    @property
    preloadNextStep:boolean = false;

    manager;

    preload(data){

    }
    show(data?){
        this.node.active = true;
    }
    hide(){
        return new Promise(resolve=>{
            this.node.active = false;
            resolve(null);
        });
    }

    nextStep(){
        this.manager?.nextStep();
    }

    start() {

    }

    update(deltaTime: number) {
        
    }
}


