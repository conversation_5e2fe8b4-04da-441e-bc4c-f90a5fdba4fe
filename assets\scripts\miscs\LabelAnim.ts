import { _decorator, color, Color, Component, instantiate, Label, Layout, Node, tween, UIOpacity, UITransform, v3 } from 'cc';
import StringManager from '../i18n/StringManager';
const { ccclass, property } = _decorator;

@ccclass('LabelAnim')
export class LabelAnim extends Component {
    @property
    _stringId:string = "";
    @property
    get stringId():string{
        return this._stringId;
    }
    set stringId(v){
        this._stringId = v;
        this.refresh();    
    }

    @property
    playOnLoad = false;

    @property
    initTime:number = 1;
    @property
    idleTime:number = 1;
    @property
    delayTime:number = 1;

    @property(Node)
    temp: Node = null;

    @property([Color])
    colors: Color[] = [];

    @property(Node)
    holder: Node = null;

    @property([Label])
    labels: Label[] = [];

    onLoad(){

    }
    refresh(){
        if(this.holder){
            this.holder.removeAllChildren();
        }
        const content : string = StringManager.i.getString(this.stringId);
        const s1 = content.split('\n');
        for(let i = 0; i < s1.length; i++){
            const s = s1[i].split(' ');
            const row = new Node().addComponent(Layout);
            row.node.name = "row"+i;
            row.type = Layout.Type.HORIZONTAL;
            row.resizeMode = Layout.ResizeMode.CONTAINER;
            row.spacingX = 15;
            row.getComponent(UITransform).height = 65;
            row.affectedByScale = true;

            for(let j = 0; j < s.length; j++){
                const node = instantiate(this.temp);
                row.node.addChild(node);
                const label = node.getComponent(Label);
                label.string = s[j];
                label.color = this.colors[(j+i)%this.colors.length];
    
                this.labels.push(label);
            }
            (this.holder||this.node).addChild(row.node);
        }
    }
    
    start() {
        if(this.playOnLoad)
            this.play();
    }

    play(){
        this.refresh();
        const t1 = this.initTime/this.labels.length;
        for(let i = 0; i < this.labels.length; i++){
            this.labels[i].node.active = true;
            const opacity = this.labels[i].addComponent(UIOpacity);
            opacity.opacity = 0;
            tween(opacity).delay(t1 * i).to(t1, {opacity:255}).delay(i*this.idleTime/2).start();
        }
    }
    play2(delayIdle=0){
        this.refresh();
        const t1 = this.initTime/this.labels.length;
        for(let i = 0; i < this.labels.length; i++){
            this.labels[i].node.active = true;
            const opacity = this.labels[i].addComponent(UIOpacity);
            opacity.opacity = 0;
            tween(opacity).delay(t1 * i).to(t1, {opacity:255}).call(()=>{
                if(i == this.labels.length-1){
                    this.node.emit("full");
                }
            }).delay(delayIdle+i*this.idleTime/2).call(()=>{
                tween(this.labels[i].node).delay(this.delayTime-t1 * i)
                    .to(this.idleTime/2, {scale:v3(1.2, 1.2, 1)}).to(this.idleTime/2, {scale:v3(1,1,1)}).start()
            }).delay(i*this.idleTime/2).call(()=>{
                if(i == this.labels.length-1){
                    this.node.emit("endIdle");
                }
            }).start();
        }
    }

    update(deltaTime: number) {
        
    }
}


