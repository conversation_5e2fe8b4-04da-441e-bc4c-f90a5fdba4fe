import { _decorator, Collider2D, Color, Component, Node, Sprite, UITransform, v3 } from 'cc';
import { ObjectDataConfig } from '../../enums/DataConfigs';
import { Bolt } from './Bolt';
const { ccclass, property } = _decorator;

@ccclass('ObjectComponent')
export class ObjectComponent extends Component {
    @property(Sprite)
    public img: Sprite = null;

    @property(Sprite)
    public shadow: Sprite = null;

    @property(Collider2D)
    public body: Collider2D = null;

    @property(Node)
    public bolts: Node = null;

    private arrBolt: Bolt[] = [];

    private _layer: number = 0;

    public get layer(): number {
        return this._layer;
    }

    public set layer(value: number) {
        this._layer = value;
    }

    protected lateUpdate(dt: number): void {
        this.updateVisual();
    }

    private updateVisual() {
        const wpos = this.img.getComponent(UITransform).convertToWorldSpaceAR(v3());
        wpos.y -= 15;
        const spos = this.img.getComponent(UITransform).convertToNodeSpaceAR(wpos);
        this.shadow.node.position = spos;
    }

    public setColliderLayer(layer: number): void {
        if (this.body) {
            this.body.group = 1 << layer;
        }
    }

    public setColor(color: Color): void {
        this.img.color = color;
    }

    public initialize(config: ObjectDataConfig, color: Color): void {

        this.layer = config.layer;

        this.setColor(color);
        this.setColliderLayer(config.layer);

        for (let i = 0; i < config.Colors.length; i++) {
            const bolt = this.bolts.children[i].getComponent(Bolt);
            if (bolt) {
                bolt.initialize(config.Colors[i], this);
                this.arrBolt.push(bolt);
            }
        }
    }

    public getBolts(): Bolt[] {
        return this.arrBolt;
    }
}


