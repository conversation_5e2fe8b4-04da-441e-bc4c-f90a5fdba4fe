import { _decorator, CCInteger, Color, Enum, Prefab, SpriteFrame, Vec3 } from 'cc';
import { ColorType, ObjectType } from './Enums';
const { ccclass, property } = _decorator;

@ccclass('BoltMapping')
export class BoltMapping {
    @property({
        type: Enum(ColorType),
        tooltip: ""
    })
    colorType: ColorType = ColorType.None;
    @property(SpriteFrame)
    spriteFrame: SpriteFrame | null = null;
}

@ccclass('BoxLidMapping')
export class BoxLidMapping {
    @property({
        type: Enum(ColorType),
        tooltip: ""
    })
    colorType: ColorType = ColorType.None;
    @property(SpriteFrame)
    spriteFrame: SpriteFrame | null = null;
}

@ccclass('BoxMapping')
export class BoxMapping {
    @property({
        type: Enum(ColorType),
        tooltip: ""
    })
    colorType: ColorType = ColorType.None;

    @property(SpriteFrame)
    spriteFrame: SpriteFrame | null = null;
}



@ccclass('ObjectPrefabMapping')
export class ObjectPrefabMapping {
    @property({
        type: Enum(ObjectType),
        tooltip: "The shape of the element this configuration applies to."
    })
    shapeID: ObjectType = ObjectType.none;
    @property(Prefab)
    prefab: Prefab | null = null;
}

@ccclass('ObjectDataConfig')
export class ObjectDataConfig {

    @property({ type: Enum(ObjectType) })
    public Type: ObjectType = ObjectType.none;

    @property(Vec3)
    public Position: Vec3 = new Vec3();

    @property(Vec3)
    public Rotation: Vec3 = new Vec3();

    @property(CCInteger)
    public layer: number = 0;

    @property({
        type: [Enum(ColorType)],
        tooltip: "Array of colors for this object"
    })
    public Colors: ColorType[] = [];
}

@ccclass('BoxDataConfig')
export class BoxDataConfig {
    @property({ type: Enum(ColorType) })
    public colorType: ColorType = ColorType.None;
    
    @property(CCInteger)
    public slot: number = 0;
}

@ccclass('LevelDataConfig')
export class LevelDataConfig {

    @property([Color])
    public ColorShapes: Color[] = [];

    @property([ObjectDataConfig])
    public ObjectDatas: ObjectDataConfig[] = [];

    @property([BoxDataConfig])
    public BoxDatas: BoxDataConfig[] = [];

}