import { _decorator, Component, Node, tween, UIOpacity } from 'cc';
import { Singleton } from '../common/Singleton';
import { BoardController } from '../game/BoardController';
import { ObjectManager } from '../game/ObjectManager';
import { GameState } from '../enums/GameState';
import { Signal } from '../eventSystem/Signal';
import { SoundManager } from './SoundManager';
import { Remarkable } from '../miscs/vfx/Remarkable';
import { LabelAnim } from '../miscs/LabelAnim';
import { BaseGameOuttro } from '../flow/outtro/BaseGameOuttro';
const { ccclass, property } = _decorator;

@ccclass('GameManager')
export class GameManager extends Singleton {

    @property(BoardController)
    public boardController: BoardController = null;

    @property(Node)
    public winNode: Node = null;
    @property(Node)
    public loseNode: Node = null;

    @property(Remarkable)
    public remarkable: Remarkable = null;

    @property(LabelAnim)
    public labelAnim: LabelAnim = null;

    public isTutorial: boolean = false;

    private _currentGameState: GameState = GameState.Loading;

    public onStateChange: Signal<GameState> = new Signal();

    public static get Instance(): GameManager {
        return this.getInstance();
    }

    public get ObjectManager(): ObjectManager {
        return this.boardController.objectManager;
    }

    public get currentGameState(): GameState {
        return this._currentGameState;
    }

    public setGameState(state: GameState): void {
        this._currentGameState = state;
        this.onStateChange?.trigger(this._currentGameState);
    }

    public showWin(): void {
        this.winNode.getComponent(BaseGameOuttro).show();
        this.boardController.tutorialSystem.node.active = false;

    }

    public showLose(): void {
        this.loseNode.getComponent(BaseGameOuttro).isLose = true;
        this.loseNode.getComponent(BaseGameOuttro).show();
        this.boardController.tutorialSystem.node.active = false;
    }

    public startTutorial(): void {
        this.setGameState(GameState.Prepare);
        // this.setGameState(GameState.Prepare);
        SoundManager.Instance.playBgm(SoundManager.Instance.BGM);
        // this.isTutorial = true;
        // this.labelAnim.play2(0.75);
    }

    public getGameState(): GameState {
        return this._currentGameState;
    }

    public async showRemarkable(): Promise<void> {
        this.labelAnim.node.active = false;
        this.remarkable.node.active = true;
        await this.remarkable.play();

        this.isTutorial = false;
        this.remarkable.node.active = false;

        this.setGameState(GameState.Prepare);

        tween(this.remarkable.getComponent(UIOpacity))
            .to(0.5, { opacity: 0 })
            .call(() => {
                
            })
            .start();
    }


    public showPicture(): void {
        this.setGameState(GameState.Picture);
    }


}


