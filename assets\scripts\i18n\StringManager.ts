import { sys } from "cc";

const config = {
    en:{
        MAP_INTRO_V1: "Goal",
        MAP_INTRO_V1_2: "Find Wools matches Box Color",
        NORMAL_INTRO: "Match Screws and Decorate!",
        NORMAL_OUTRO: "Screws Match and Decoration Game",
        PLAY_NOW: "Play Now",
        DOWNLOAD_NOW: "Download Now!!",
        BTN_DOWNLOAD: "Download Now",
        LET_TRY: "Let's Try it",
        DECOR_INTRO: "Let's decorate this room",
        TAP_TO_CONTINUE: "Tap To Continue",
    },
    de:{
        MAP_INTRO_V1: "Ziel",
        MAP_INTRO_V1_2: "<PERSON><PERSON>, die zur\nKastenfarbe passen",
        NORMAL_INTRO: "<PERSON><PERSON>uben zusammenfügen und dekorieren!",
        NORMAL_OUTRO: "Schrauben-Verbindungs- und Dekorationsspiel",
        PLAY_NOW: "<PERSON><PERSON><PERSON>",
        DOWNLOAD_NOW: "<PERSON>zt Her<PERSON>laden!!",
        BTN_DOWNLOAD: "Jetzt Herunterladen",
        LET_TRY: "Lass es uns versuchen",
        DECOR_INTRO: "Lass es uns versuchen",
        TAP_TO_CONTINUE: "Tippe, um fortzufahren",
    },
    es:{
        MAP_INTRO_V1: "Objetivo",
        MAP_INTRO_V1_2: "Encuentra los tornillos que\ncoincidan con el color de la caja",
        NORMAL_INTRO: "¡Combina los tornillos y decora!",
        NORMAL_OUTRO: "Juego de combinación de tornillos y decoración",
        PLAY_NOW: "Jugar Ahora",
        DOWNLOAD_NOW: "¡Descargar Ahora!!",
        BTN_DOWNLOAD: "¡Descargar Ahora",
        LET_TRY: "Intentémoslo",
        DECOR_INTRO: "Vamos a decorar esta habitación",
        TAP_TO_CONTINUE: "Toca para continuar",
    },
    pt:{
        MAP_INTRO_V1: "Objetivo",
        MAP_INTRO_V1_2: "Encontre os parafusos que\ncombinem com a cor da caixa",
        NORMAL_INTRO: "Combine os parafusos e decore!",
        NORMAL_OUTRO: "Jogo de combinação de parafusos e decoração",
        PLAY_NOW: "Jogar Agora",
        DOWNLOAD_NOW: "Baixar Agora!!",
        BTN_DOWNLOAD: "Baixar Agora",
        LET_TRY: "Vamos tentar",
        DECOR_INTRO: "Vamos decorar este quarto",
        TAP_TO_CONTINUE: "Toque para continuar",
    },
    kr:{
        MAP_INTRO_V1: "목표",
        MAP_INTRO_V1_2: "상자 색상과 일치하는\n나사를 찾으세요",
        NORMAL_INTRO: "나사를 맞추고 장식하세요!",
        NORMAL_OUTRO: "나사 맞추기 및 장식 게임",
        PLAY_NOW: "지금 플레이하기",
        DOWNLOAD_NOW: "지금 다운로드하기!!",
        BTN_DOWNLOAD: "지금 다운로드하기",
        LET_TRY: "해보자",
        DECOR_INTRO: "이 방을 꾸며봅시다",
        TAP_TO_CONTINUE: "탭하여 계속하기",
    }
};

export default class StringManager{
    private static _i : StringManager = null;
    public static get i():StringManager{
        if(this._i == null)
            this._i = new StringManager();
        return this._i;
    }

    // data = JSON.parse(JSON.stringify(config[Intl.DateTimeFormat().resolvedOptions().locale] || config.en));
    data = JSON.parse(JSON.stringify(config[sys.language] || config.en));
    
    getString(id) : string{
        return this.data[id] || id;
    }
}