import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Singleton')
export class <PERSON>ton extends Component {
    private static _instances: Map<Function, Component> = new Map();

    public static getInstance<T extends Component>(this: new (...args: any[]) => T): T {
        if (!Singleton._instances.has(this)) {
            Singleton._instances.set(this, new this());
        }
        return Singleton._instances.get(this)! as T;
    }

    protected onLoad() {
        const constructor = this.constructor as Function;
        if (Singleton._instances.has(constructor)) {
            this.node.destroy();
            return;
        }
        Singleton._instances.set(constructor, this);
    }

    protected onDestroy() {
        const constructor = this.constructor as Function;
        if (Singleton._instances.get(constructor) === this) {
            Singleton._instances.delete(constructor);
        }
    }
}


