import { _decorator, Component, Joint2D, Node, Sprite, Intersection2D, Vec2, PolygonCollider2D, UITransform, Vec3, tween } from 'cc';
import { ColorType } from '../../enums/Enums';
import { ColorHolder } from '../../app/ColorHolder';
import { UIButton } from '../../eventSystem/UIButton';
import { GameManager } from '../../app/GameManager';
import { ObjectComponent } from './ObjectComponent';
import { RopeManager } from '../../app/RopeManager';
import { SoundManager } from '../../app/SoundManager';
import { GameState } from '../../enums/GameState';
import { Signal } from '../../eventSystem/Signal';
const { ccclass, property } = _decorator;

@ccclass('Bolt')
export class Bolt extends Component {
    @property(Sprite)
    public boltImg: Sprite | null = null;

    @property(Joint2D)
    public joint: Joint2D | null = null;

    @property(UIButton)
    public button: UIButton | null = null;

    private _objectComponent: ObjectComponent | null = null;

    private _color: ColorType = ColorType.None;
    private _shapeAngle: number = 0;

    private readonly BOLT_RADIUS = 37.5;

    private canTouch: boolean = true;

    public static onTap = new Signal();
    
    public isCollected: boolean = false;

    protected onLoad(): void {
        this.button.InteractedEvent.on(this.InteractedBolt, this);
    }

    private InteractedBolt(): void {


        Bolt.onTap?.trigger();

        if (!this.canTouch) return;

        if (this.isOverlappingWithAnyCollider()) {
            this.playAnimationShake();
            return
        }

        SoundManager.Instance.playSfx(SoundManager.Instance.SelectBolt);

        const currentBox = GameManager.Instance.boardController.boxManager.currentBox;

        if (currentBox.checkCorrect(this._color) && GameManager.Instance.getGameState() != GameState.DrawBoard) {

            const color = GameManager.Instance.boardController.getColor(this._color);
            const rope = RopeManager.Instance.getRope();
            const startPos = this.node.getWorldPosition().clone();

            const arrPos = currentBox.getBoxLid().getPosPerBoxLid2();
            const endPos = currentBox.getBoxLid().getPosPerBoxLid();

            rope.setAnimationDuration(0.2);

            rope.createLine2(startPos, arrPos, endPos, color, 20, () => {
                currentBox.checkCompleteLid();
            }, (pos) => {

            });

            currentBox.getBoxLid().playAnimationAppear();
            this.playAnimationCollect(() => {
                SoundManager.Instance.playSfx(SoundManager.Instance.RemoveBolt);
            });

            currentBox.IncreaseLidCount();

            this.isCollected = true;
           
        }
        else {
            const result = GameManager.Instance.boardController.holeManager.setBolt(this);

            console.log(result);
            if (result.flag) {
                this.joint.enabled = false
                this.isCollected = true;
                this.flyToHole(result.hole.node, () => {
                    const lose = GameManager.Instance.boardController.holeManager.Lose();
                    if (lose) {
                        SoundManager.Instance.playSfx(SoundManager.Instance.Lose);
                        GameManager.Instance.showLose();
                    }
                });

                this.canTouch = false;
            }
            else {
                console.log("No hole found");

            }
        }

    }



    private flyToHole(targetNode: Node, callback: () => void = () => { }): void {
        const top = GameManager.Instance.boardController.objectManager;
        const topNode = top.node;

        // Lưu world position hiện tại của bolt
        const boltWorldPos = this.node.getWorldPosition();

        // Đổi parent sang topNode
        this.node.setParent(topNode);
        this.node.angle = 0;

        // Đặt lại vị trí bolt theo local của topNode
        let boltLocalPos: Vec3;
        const topTransform = topNode.getComponent(UITransform);
        if (topTransform && topTransform.convertToNodeSpaceAR) {
            boltLocalPos = new Vec3();
            topTransform.convertToNodeSpaceAR(boltWorldPos, boltLocalPos);
        } else {
            boltLocalPos = new Vec3();
            topNode.inverseTransformPoint(boltWorldPos, boltLocalPos);
        }
        this.node.setPosition(boltLocalPos);

        // Lấy world position của hole, chuyển về local của topNode
        const holeWorldPos = targetNode.getWorldPosition();
        let holeLocalPos1: Vec3, holeLocalPos2: Vec3;
        if (topTransform && topTransform.convertToNodeSpaceAR) {
            holeLocalPos1 = new Vec3();
            holeLocalPos2 = new Vec3();
            topTransform.convertToNodeSpaceAR(new Vec3(holeWorldPos.x, holeWorldPos.y + 50, holeWorldPos.z), holeLocalPos1);
            topTransform.convertToNodeSpaceAR(new Vec3(holeWorldPos.x, holeWorldPos.y + 15, holeWorldPos.z), holeLocalPos2);
        } else {
            holeLocalPos1 = new Vec3();
            holeLocalPos2 = new Vec3();
            topNode.inverseTransformPoint(new Vec3(holeWorldPos.x, holeWorldPos.y + 50, holeWorldPos.z), holeLocalPos1);
            topNode.inverseTransformPoint(new Vec3(holeWorldPos.x, holeWorldPos.y + 15, holeWorldPos.z), holeLocalPos2);
        }

        // Tween bolt về vị trí hole (y+50) trong 0.2s, sau đó tới (y+15) trong 0.3s
        tween(this.node)
            .to(0.3, { position: holeLocalPos1 })
            .to(0.2, { position: holeLocalPos2 })
            .call(callback)
            .start();
    }



    private isLineCircleIntersecting(p1: Vec2, p2: Vec2, center: Vec2, radius: number): boolean {
        const line = new Vec2(p2.x - p1.x, p2.y - p1.y);
        const lineLength = line.length();

        const toCenter = new Vec2(center.x - p1.x, center.y - p1.y);

        const dot = toCenter.dot(line) / (lineLength * lineLength);
        const projection = new Vec2(line.x * dot, line.y * dot);

        const closest = new Vec2(p1.x + projection.x, p1.y + projection.y);

        if (dot < 0) return toCenter.length() <= radius;
        if (dot > 1) return new Vec2(center.x - p2.x, center.y - p2.y).length() <= radius;

        const distance = new Vec2(center.x - closest.x, center.y - closest.y).length();
        return distance <= radius;
    }

    public isOverlappingWithAnyCollider(): boolean {
        const boltWorldPos = this.node.getWorldPosition();
        const boltPos2D = new Vec2(boltWorldPos.x, boltWorldPos.y);

        const objectManager = GameManager.Instance.ObjectManager;
        if (!objectManager) return false;

        const glassNodes = objectManager.node.children;
        for (const node of glassNodes) {
            if (!node.activeInHierarchy) continue;

            const glassComponent = node.getComponent(ObjectComponent);
            if (!glassComponent || glassComponent.layer >= this._objectComponent.layer) {
                continue;
            }

            const polygonCollider = node.getComponent(PolygonCollider2D);
            if (polygonCollider) {
                const uiTransform = node.getComponent(UITransform);
                if (!uiTransform) {
                    continue;
                }

                const worldPoints = polygonCollider.points.map(localPoint => {
                    const localVec3 = new Vec3(localPoint.x, localPoint.y, 0);
                    const worldVec3 = new Vec3();
                    uiTransform.convertToWorldSpaceAR(localVec3, worldVec3);
                    return new Vec2(worldVec3.x, worldVec3.y);
                });

                if (Intersection2D.pointInPolygon(boltPos2D, worldPoints)) {
                    return true;
                }

                for (let i = 0; i < worldPoints.length; i++) {
                    const p1 = worldPoints[i];
                    const p2 = worldPoints[(i + 1) % worldPoints.length];
                    if (this.isLineCircleIntersecting(p1, p2, boltPos2D, this.BOLT_RADIUS)) {
                        return true;
                    }
                }
            }
        }

        return false;
    }

    public initialize(color: ColorType, shape: ObjectComponent): void {

        this._color = color;

        const mapping = ColorHolder.Instance.boltMappings.find(mapping => mapping.colorType === color);
        if (mapping) {
            this.boltImg.spriteFrame = mapping.spriteFrame;
        }

        this._objectComponent = shape;

        this._shapeAngle = shape.node.angle;
        this.node.angle = -this._shapeAngle;
    }

    private playAnimationShake(): void {



        const originalPosition = this.boltImg.node.position.clone();
        const shakeDuration = .5;
        const shakeStrength = 5;
        const shakeFrequency = 0.1;

        tween(this.boltImg.node)
            .repeat(
                shakeDuration / shakeFrequency,
                tween()
                    .to(shakeFrequency / 2, { position: new Vec3(originalPosition.x + shakeStrength, originalPosition.y, originalPosition.z) })
                    .to(shakeFrequency / 2, { position: new Vec3(originalPosition.x - shakeStrength, originalPosition.y, originalPosition.z) })
            )
            .to(0, { position: originalPosition })
            .start();
    }

    public playAnimationCollect(callback: () => void = () => { }): void {
        tween(this.node)
            .to(0.5, { scale: Vec3.ZERO })

            .call(() => {
                this.node.destroy();
                callback();
            })

            .start();
    }

    public get colorType(): ColorType {
        return this._color;
    }

}


