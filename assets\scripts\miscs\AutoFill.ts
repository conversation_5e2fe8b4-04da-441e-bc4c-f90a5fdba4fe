import { _decorator, Component, Node, UITransform, v3, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AutoFillSprite')
export class AutoFillSprite extends Component {
    refresh(){
        const transform = this.getComponent(UITransform);
        if(!transform)
            return;
        const transform2 = this.node.parent.getComponent(UITransform);
        const scaleX = transform2.width/transform.width;
        const scaleY = transform2.height/transform.height;
        const scale = Math.max(scaleX, scaleY);
        this.node.scale = v3(scale, scale, 1);
    }

    update(deltaTime: number) {
        
    }

    protected lateUpdate(dt: number): void {
        this.refresh();
    }
}


