[{"__type__": "cc.SceneAsset", "_name": "generateLevel-010", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "scene": {"__id__": 1}}, {"__type__": "cc.Scene", "_name": "generateLevel-010", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 4}, {"__id__": 11}, {"__id__": 216}], "_active": true, "_components": [], "_prefab": {"__id__": 266}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "autoReleaseAssets": false, "_globals": {"__id__": 271}, "_id": "68fbd063-b901-4beb-86e0-d2df63a76d4b"}, {"__type__": "cc.Node", "_name": "Main Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 3}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -10, "y": 10, "z": 10}, "_lrot": {"__type__": "cc.Quat", "x": -0.27781593346944056, "y": -0.36497167621709875, "z": -0.11507512748638377, "w": 0.8811195706053617}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": -35, "y": -45, "z": 0}, "_id": "c9DMICJLFO5IeO07EPon7U"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": null, "_projection": 1, "_priority": 0, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 10, "_near": 1, "_far": 1000, "_color": {"__type__": "cc.Color", "r": 51, "g": 51, "b": 51, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 7, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 1822425087, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "7dWQTpwS5LrIHnc1zAPUtf"}, {"__type__": "cc.Node", "_name": "Manager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 5}, {"__id__": 193}, {"__id__": 212}, {"__id__": 214}], "_active": true, "_components": [], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b2n4CbHPFGBp7k9YFuKMB2"}, {"__type__": "cc.Node", "_name": "GameManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 6}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "99JkRm8m5O5rxb//usMOD+"}, {"__type__": "588209yocVGk5FsFNmGlRS0", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 5}, "_enabled": true, "__prefab": null, "boardController": {"__id__": 7}, "winNode": {"__id__": 19}, "loseNode": {"__id__": 87}, "remarkable": null, "labelAnim": null, "_id": "ffn0ej+2JBa5i9XHkuTOvP"}, {"__type__": "65f92eyZhhBZoJrwWiPVFI4", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "pixelPrefab": {"__uuid__": "54c2ce62-4d46-4bbb-9e26-5ea61a27e5d4", "__expectedType__": "cc.Prefab"}, "tut": {"__uuid__": "3a8e0c21-483d-4c2b-a039-c05610a9d216", "__expectedType__": "cc.Json<PERSON>set"}, "level": {"__uuid__": "bd5f1336-dc60-4621-8db5-f2dac4b0d072", "__expectedType__": "cc.Json<PERSON>set"}, "objectManager": {"__id__": 136}, "holeManager": {"__id__": 133}, "boxManager": {"__id__": 168}, "pictureData": {"__uuid__": "bd5f1336-dc60-4621-8db5-f2dac4b0d072", "__expectedType__": "cc.Json<PERSON>set"}, "levelData": {"__id__": 192}, "picture": {"__id__": 181}, "tutorialPrefab": {"__uuid__": "d6b2ecf3-7d4e-4932-81e1-a5a688ce7bcf", "__expectedType__": "cc.Prefab"}, "_id": "1dG5Nm/2VMUauy3qdAsDVP"}, {"__type__": "cc.Node", "_name": "Board", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 190}, {"__id__": 7}, {"__id__": 191}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "5cuL5G619EPaLEJ+k7m+oY"}, {"__type__": "cc.Node", "_name": "Grid", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 175}, {"__id__": 8}, {"__id__": 179}, {"__id__": 183}, {"__id__": 186}], "_active": true, "_components": [{"__id__": 189}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 506.323, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.8, "y": 1.8, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bdhEy7VBtCRZgwHkc6ABca"}, {"__type__": "cc.Node", "_name": "gameplay", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 9}, {"__id__": 110}, {"__id__": 134}, {"__id__": 166}, {"__id__": 170}], "_active": true, "_components": [{"__id__": 173}, {"__id__": 174}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "11meUPW/JIiIfR04FivdsR"}, {"__type__": "cc.Node", "_name": "<PERSON><PERSON>", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 12}, {"__id__": 14}, {"__id__": 10}, {"__id__": 19}, {"__id__": 43}, {"__id__": 60}, {"__id__": 72}, {"__id__": 83}, {"__id__": 87}], "_active": true, "_components": [{"__id__": 107}, {"__id__": 108}, {"__id__": 109}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 540, "y": 960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "fe9jhXFz1Gj4L6vcCrck2W"}, {"__type__": "cc.Node", "_name": "Camera", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 13}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 1000}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "9d82COF3RHYKGEqbvO9SwN"}, {"__type__": "cc.Camera", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 12}, "_enabled": true, "__prefab": null, "_projection": 0, "_priority": 1073741824, "_fov": 45, "_fovAxis": 0, "_orthoHeight": 960, "_near": 1, "_far": 2000, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_depth": 1, "_stencil": 0, "_clearFlags": 6, "_rect": {"__type__": "cc.Rect", "x": 0, "y": 0, "width": 1, "height": 1}, "_aperture": 19, "_shutter": 7, "_iso": 0, "_screenScale": 1, "_visibility": 41943040, "_targetTexture": null, "_postProcess": null, "_usePostProcess": false, "_cameraType": -1, "_trackingType": 0, "_id": "0bFVfo3xdG37grM6Eh3VEK"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": true, "_components": [{"__id__": 15}, {"__id__": 16}, {"__id__": 17}, {"__id__": 18}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b5uCQY1PlEaL1cjyXsNYVz"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "22oqL7EdlEcrNbFXb1PT7K"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "1ea86903-fcbb-47a6-a908-305e5ae4459c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a3FzRDHyFP0Z1Gy/g76h8G"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": false, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 40, "_originalHeight": 36, "_alignMode": 2, "_lockFlags": 0, "_id": "e5GXFenFlDuYScz4GjEgNB"}, {"__type__": "46dddqGL6hDh4qqNaXQ4Aie", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 14}, "_enabled": true, "__prefab": null, "_id": "58KImIs6FFyb9g4RKRt31t"}, {"__type__": "cc.Node", "_name": "normal_outtro_design2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 20}, {"__id__": 24}, {"__id__": 34}], "_active": false, "_components": [{"__id__": 38}, {"__id__": 39}, {"__id__": 40}, {"__id__": 41}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "87wcpr6FZLB7Mr0bP7yP3D"}, {"__type__": "cc.Node", "_name": "Designer_2", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 21}, {"__id__": 22}, {"__id__": 23}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "c9wAkQW01IuKbYOIbLaWck"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 864, "height": 1536}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8e0P99/SRIyrlJNh0goTUL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "90a59c66-0ef6-43a1-b679-59f3b93031e6@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "22NPeNuv1KVq2jRWcvXtwi"}, {"__type__": "46dddqGL6hDh4qqNaXQ4Aie", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 20}, "_enabled": true, "__prefab": null, "_id": "aaMSiQQHBMG6cVWjxv099y"}, {"__type__": "cc.Node", "_name": "btn_cta", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [{"__id__": 25}, {"__id__": 28}], "_active": true, "_components": [{"__id__": 31}, {"__id__": 32}, {"__id__": 33}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -709.4999999999999, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "f79/bOmbdAuZGs/impd7dV"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 26}, {"__id__": 27}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 131.374, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "dd39FBIfdP3qYVWjHCAjSi"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 564.0797729492188, "height": 88.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "83jqxW9ShHnabZatp7UMbP"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Match Wool and Decorate", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 48, "_fontSize": 48, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "710f153b-4826-4c63-92fd-43a6e2030ddf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 102, "g": 58, "b": 10, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "98SKl5svJEmq6GJ2EDSFIb"}, {"__type__": "cc.Node", "_name": "Label", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 24}, "_children": [], "_active": true, "_components": [{"__id__": 29}, {"__id__": 30}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 7.631, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3f7he9ub9LsqljKpesjsF3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 192.57595825195312, "height": 88.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "626/Bo1tlPYrqv3lRBQBhF"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 28}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "Install", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 64, "_fontSize": 64, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "710f153b-4826-4c63-92fd-43a6e2030ddf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 66, "g": 111, "b": 18, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "b3Hto2ydJGe5DD0xT6vh6A"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 443, "height": 165}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "acQgmRSlRNe5DmXy8r0o4y"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "32734615-a3c5-446d-992a-9c595c41f592@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "f7Wf2h85FOYojSLDiwbnvu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 24}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 168, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "53V33GQRBOF4WCzuR3ygmQ"}, {"__type__": "cc.Node", "_name": "GameTitle", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 19}, "_children": [], "_active": true, "_components": [{"__id__": 35}, {"__id__": 36}, {"__id__": 37}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 604.8, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1.2, "y": 1.2, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "bb5dma97ZBJbXfu8B5IHbe"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 598, "height": 432}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aeU5zIt45EILmW1cMor9+P"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "15c3999a-5b63-4efe-bae5-c1a62b4d384f@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "77ByPHstlGV6krG+RjdkGh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 34}, "_enabled": true, "__prefab": null, "_alignFlags": 1, "_target": null, "_left": 0, "_right": 0, "_top": 96, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "98lSTUrYRDpps+6BFpFdTV"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e6cgAAsf1AJqIh6/hkVSTu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "e6dExP6aZDS6iY5BNjGW/o"}, {"__type__": "b8e795MFZ9Ec7meU3D7knf5", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "layer": 1, "_refreshOnLoad": false, "preloadNextStep": false, "_id": "bbUx525/NBUKwXD7CFXZlh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 42}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "0d119ro7VJDaggh2uVD0Jx"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 19}, "component": "", "_componentId": "b8e795MFZ9Ec7meU3D7knf5", "handler": "cta", "customEventData": ""}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 44}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 43}, "asset": {"__uuid__": "74342968-2ffb-4924-a2ba-1f264327f62a", "__expectedType__": "cc.Prefab"}, "fileId": "8e6Yln56VHC6Y4Y5gS3CEy", "instance": {"__id__": 45}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "b3fTdokrJIX7xW2DPAKthN", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 46}, {"__id__": 48}, {"__id__": 49}, {"__id__": 50}, {"__id__": 51}, {"__id__": 53}, {"__id__": 55}, {"__id__": 56}, {"__id__": 58}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_name"], "value": "remarkable"}, {"__type__": "cc.TargetInfo", "localID": ["8e6Yln56VHC6Y4Y5gS3CEy"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 52}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}}, {"__type__": "cc.TargetInfo", "localID": ["5apHXb6YRDJq6u6j24P/ub"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 54}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}}, {"__type__": "cc.TargetInfo", "localID": ["f8DJ4B+GFBjp1aJrynrhb1"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 47}, "propertyPath": ["_active"], "value": false}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 57}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["cczJqMg31P2LWeaC77uUeR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 59}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}, {"__type__": "cc.TargetInfo", "localID": ["d1uCME8VNNDqHsaHgemDdz"]}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 61}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 60}, "asset": {"__uuid__": "ff792c90-1da6-47c8-928f-d27b3ef47514", "__expectedType__": "cc.Prefab"}, "fileId": "39QxL30RpIKYOzplVCtxbl", "instance": {"__id__": 62}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "c8F5AFeEZIjYbpVFgf7S9m", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [{"__id__": 63}], "propertyOverrides": [{"__id__": 66}, {"__id__": 68}, {"__id__": 69}, {"__id__": 70}, {"__id__": 71}], "removedComponents": []}, {"__type__": "cc.MountedComponentsInfo", "targetInfo": {"__id__": 64}, "components": [{"__id__": 65}]}, {"__type__": "cc.TargetInfo", "localID": ["39QxL30RpIKYOzplVCtxbl"]}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {"mountedRoot": {"__id__": 60}}, "node": {"__id__": 60}, "_enabled": true, "__prefab": null, "_alignFlags": 4, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 320, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "5cAYpHuwpO/K4OSatk+oHL"}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_name"], "value": "text-001"}, {"__type__": "cc.TargetInfo", "localID": ["39QxL30RpIKYOzplVCtxbl"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": 0, "y": -580.0559999999999, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 67}, "propertyPath": ["_lscale"], "value": {"__type__": "cc.Vec3", "x": 0.8, "y": 0.8, "z": 1}}, {"__type__": "cc.Node", "_objFlags": 0, "_parent": {"__id__": 11}, "_prefab": {"__id__": 73}, "__editorExtras__": {}}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 72}, "asset": {"__uuid__": "54c2ce62-4d46-4bbb-9e26-5ea61a27e5d4", "__expectedType__": "cc.Prefab"}, "fileId": "3dkRVyMmVHeriZerDUf7VR", "instance": {"__id__": 74}, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInstance", "fileId": "a196IJ0GVDyLLLSCoTa4hu", "prefabRootNode": null, "mountedChildren": [], "mountedComponents": [], "propertyOverrides": [{"__id__": 75}, {"__id__": 77}, {"__id__": 78}, {"__id__": 79}, {"__id__": 80}, {"__id__": 82}], "removedComponents": []}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_name"], "value": "pixel"}, {"__type__": "cc.TargetInfo", "localID": ["3dkRVyMmVHeriZerDUf7VR"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_lpos"], "value": {"__type__": "cc.Vec3", "x": -1443.183, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_lrot"], "value": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_euler"], "value": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 81}, "propertyPath": ["_contentSize"], "value": {"__type__": "cc.Size", "width": 100, "height": 100}}, {"__type__": "cc.TargetInfo", "localID": ["807UqyCN1PbpjZhFrDuJD5"]}, {"__type__": "CCPropertyOverrideInfo", "targetInfo": {"__id__": 76}, "propertyPath": ["_active"], "value": false}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [], "_active": false, "_components": [{"__id__": 84}, {"__id__": 85}, {"__id__": 86}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -2597.891, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "35/qSZ+F1IwqI1dcdBzxzK"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "18aPGdbQ5LF4GAEoDiTi4z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 172, "g": 162, "b": 237, "a": 255}, "_spriteFrame": {"__uuid__": "f1c9df4d-c0cb-4702-9fd1-be822fe37587@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "e6YDazGDpLnbLGVoTZr+0q"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 83}, "_enabled": false, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "76U9iZ8dtA+bV4FCuH2dIR"}, {"__type__": "cc.Node", "_name": "TryAgainContain", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 11}, "_children": [{"__id__": 88}, {"__id__": 92}], "_active": false, "_components": [{"__id__": 102}, {"__id__": 103}, {"__id__": 104}, {"__id__": 105}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "ebmSw7BCZPnIuvG9yqCaC6"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 87}, "_children": [], "_active": true, "_components": [{"__id__": 89}, {"__id__": 90}, {"__id__": 91}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "70XxPdXzFLy5DSq5KQmylu"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "55JSESEaVNq4JT7kT7AjQ1"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 193}, "_spriteFrame": {"__uuid__": "f1c9df4d-c0cb-4702-9fd1-be822fe37587@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "ffw+ojhNxCU6qii3dKPQL2"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 88}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "8chqnPdVdKx4Br15UGv7Iq"}, {"__type__": "cc.Node", "_name": "btnCTa", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 87}, "_children": [{"__id__": 93}], "_active": true, "_components": [{"__id__": 99}, {"__id__": 100}, {"__id__": 101}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6dphkpsFJCbYGQDbs3JUbZ"}, {"__type__": "cc.Node", "_name": "bg", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 92}, "_children": [{"__id__": 94}], "_active": true, "_components": [{"__id__": 97}, {"__id__": 98}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -68, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "251kZrdUFPoJcURmXSUylx"}, {"__type__": "cc.Node", "_name": "Label-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [], "_active": true, "_components": [{"__id__": 95}, {"__id__": 96}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 18.985000000000014, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "6bd4nSvdxGDJZuui8Unz+l"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 341.679443359375, "height": 88.64}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "643IGOgD9NkqCuYWv2UJFK"}, {"__type__": "cc.Label", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_string": "TRY AGAIN", "_horizontalAlign": 1, "_verticalAlign": 1, "_actualFontSize": 69.3, "_fontSize": 69.3, "_fontFamily": "<PERSON><PERSON>", "_lineHeight": 64, "_overflow": 0, "_enableWrapText": true, "_font": {"__uuid__": "710f153b-4826-4c63-92fd-43a6e2030ddf", "__expectedType__": "cc.TTFFont"}, "_isSystemFontUsed": false, "_spacingX": 0, "_isItalic": false, "_isBold": false, "_isUnderline": false, "_underlineHeight": 2, "_cacheMode": 1, "_enableOutline": true, "_outlineColor": {"__type__": "cc.Color", "r": 136, "g": 84, "b": 28, "a": 255}, "_outlineWidth": 4, "_enableShadow": false, "_shadowColor": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 255}, "_shadowOffset": {"__type__": "cc.Vec2", "x": 2, "y": 2}, "_shadowBlur": 2, "_id": "98RCAeF2REeJF46Xzd4FI3"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 428, "height": 184}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "fbNqpnU+FCcbYZYKXwjdOP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-4b84-8a61-d52aa007ff84@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "c6pWeyLDZHHYDuI9ko1oEX"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 428, "height": 184}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "bdLsClW/dGSp30Fxcpini+"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": false, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "*************-4b84-8a61-d52aa007ff84@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "4cwJakmFNG0Z13kFC0+djB"}, {"__type__": "1127a+QSypGH4G/tAWnqv1o", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 92}, "_enabled": true, "__prefab": null, "ctaNode": {"__id__": 92}, "scaleAmount": 1.1, "duration": 0.5, "useBackEasing": true, "_id": "ddjHFVJaRHd561qyNWg4Ut"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1919.9999999999998}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e0g6dmbbBGMZ9ULy9EF9X9"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1, "_originalHeight": 1, "_alignMode": 2, "_lockFlags": 0, "_id": "f4rtprcX9OI4r3ScaRJRfC"}, {"__type__": "b8e795MFZ9Ec7meU3D7knf5", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "layer": 1, "_refreshOnLoad": false, "preloadNextStep": false, "_id": "bcUB6OAiNOQK5uxUuu6Kk8"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 87}, "_enabled": true, "__prefab": null, "clickEvents": [{"__id__": 106}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": "4e3PCFrQtJBoxW3Lkze1gP"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 87}, "component": "", "_componentId": "b8e795MFZ9Ec7meU3D7knf5", "handler": "cta", "customEventData": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "e7K/HOqCRGh7BceejPSZtP"}, {"__type__": "cc.<PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_cameraComponent": {"__id__": 13}, "_alignCanvasWithScreen": true, "_id": "33eTNFBi1HYp5zwEEbcmnh"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 11}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 0, "_originalHeight": 0, "_alignMode": 2, "_lockFlags": 0, "_id": "69MMy8jKBHoqVP8aEVAWJt"}, {"__type__": "cc.Node", "_name": "Holes", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [{"__id__": 111}, {"__id__": 115}, {"__id__": 119}, {"__id__": 123}, {"__id__": 127}], "_active": true, "_components": [{"__id__": 131}, {"__id__": 132}, {"__id__": 133}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 89.498, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "89OPK41exE8oWRrd4AOtur"}, {"__type__": "cc.Node", "_name": "ingame_hole", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 112}, {"__id__": 113}, {"__id__": 114}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -204, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "71zOIn+NJLc5oE7pwe+wq5"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "9cD5SrNvNOibNKdsLcWlDo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e211405e-3c3b-40e9-ad96-4980e3ad6b10@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "96ElfUVZlH45g5uZSsn/6f"}, {"__type__": "51c26oa+K5FK7HuZkCkdgY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 111}, "_enabled": true, "__prefab": null, "_id": "fdl1w5eFRL2pS9SfE/KIsO"}, {"__type__": "cc.Node", "_name": "ingame_hole-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 116}, {"__id__": 117}, {"__id__": 118}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": -102, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "84/7wOqDRM5ZRCKi/RpSqP"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "c50YJkC4BHOIuB7EeQj2p0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e211405e-3c3b-40e9-ad96-4980e3ad6b10@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d2voZZ6cpGS7Ne4i8vN9FQ"}, {"__type__": "51c26oa+K5FK7HuZkCkdgY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 115}, "_enabled": true, "__prefab": null, "_id": "6bQRKGh3ZCTKnZ03oiev0V"}, {"__type__": "cc.Node", "_name": "ingame_hole-002", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 120}, {"__id__": 121}, {"__id__": 122}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "cf1uCPR45MrqqVLlkpnb2d"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7cn1Bk5ARN14ndWZDZ7iI5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e211405e-3c3b-40e9-ad96-4980e3ad6b10@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "95AVU9Ed9KeY5D+x4g6gKP"}, {"__type__": "51c26oa+K5FK7HuZkCkdgY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 119}, "_enabled": true, "__prefab": null, "_id": "08kL/RgiJP94LC8X18489/"}, {"__type__": "cc.Node", "_name": "ingame_hole-003", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 124}, {"__id__": 125}, {"__id__": 126}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 102, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2f6YgWdQJDh4PArWEiaTTg"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "db0Fz6FdpG965mr/56px/D"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e211405e-3c3b-40e9-ad96-4980e3ad6b10@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "313dGA8+tPcLLP1d+Rtqb/"}, {"__type__": "51c26oa+K5FK7HuZkCkdgY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 123}, "_enabled": true, "__prefab": null, "_id": "6e9YahyOtDr7rybj5zH89p"}, {"__type__": "cc.Node", "_name": "ingame_hole-004", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 110}, "_children": [], "_active": true, "_components": [{"__id__": 128}, {"__id__": 129}, {"__id__": 130}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 204, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "acIHJBrBREubaF5TynSIgw"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 78, "height": 105}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "d53DHRUihA3aMtpz5uufUt"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e211405e-3c3b-40e9-ad96-4980e3ad6b10@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "d3S2StJeBAKIZ2AXaKRDvp"}, {"__type__": "51c26oa+K5FK7HuZkCkdgY9", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": null, "_id": "cbHGmSoMtD+IR1PYsGqEb4"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 486, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "aavBugRcZBgKV/YTIJRnM9"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": null, "_resizeMode": 1, "_layoutType": 1, "_cellSize": {"__type__": "cc.Size", "width": 40, "height": 40}, "_startAxis": 0, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 24, "_spacingY": 0, "_verticalDirection": 1, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "93mADc94RETrxyJDKceool"}, {"__type__": "02b3b7UzTJELInxDO1II7SS", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 110}, "_enabled": true, "__prefab": null, "_id": "94Su9L9XFBtrnCpVG2TYPF"}, {"__type__": "cc.Node", "_name": "ObjectComponent", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 136}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -208.826, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "3cEGfO8/NCVZ2u4J5jt7fo"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 300, "height": 300}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "3duUqK3gRKvrEkmgRFVVF2"}, {"__type__": "4e0f1mHcoBCW5uChSYQKz66", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": null, "objects": [{"__id__": 137}, {"__id__": 138}, {"__id__": 139}, {"__id__": 140}, {"__id__": 141}, {"__id__": 142}, {"__id__": 143}, {"__id__": 144}, {"__id__": 145}, {"__id__": 146}, {"__id__": 147}, {"__id__": 148}, {"__id__": 149}, {"__id__": 150}, {"__id__": 151}, {"__id__": 152}, {"__id__": 153}, {"__id__": 154}, {"__id__": 155}, {"__id__": 156}, {"__id__": 157}, {"__id__": 158}, {"__id__": 159}, {"__id__": 160}, {"__id__": 161}, {"__id__": 162}, {"__id__": 163}, {"__id__": 164}, {"__id__": 165}], "_id": "56uuJcSYJKWrzeGW6ECflI"}, {"__type__": "ObjectPrefabMapping", "shapeID": 150, "prefab": {"__uuid__": "25d4f4aa-d72e-48db-b2a7-402d0c69ecd0", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 511, "prefab": {"__uuid__": "0502e7d0-297d-49fc-934d-4cb4d156e187", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 733, "prefab": {"__uuid__": "9824b569-f0ab-4916-ab13-c8ee3481bc00", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 405, "prefab": {"__uuid__": "a9e75844-6055-4d30-bc5c-1dc60b48f41d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 134, "prefab": {"__uuid__": "c5da247f-8a8b-4cd6-9975-8bb7af859592", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 672, "prefab": {"__uuid__": "6fdf54f9-e731-4788-a844-1e4e70ec4666", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 166, "prefab": {"__uuid__": "56d28b94-a455-4f9a-ab84-e6353341d4ce", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 617, "prefab": {"__uuid__": "023e59d3-a127-4bfb-915a-6548e545120d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 632, "prefab": {"__uuid__": "2666740c-49d2-441f-bfb2-1ecd87bd7c0f", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 522, "prefab": {"__uuid__": "7c436be1-3738-42b9-9fa1-f4d29ad6e0c0", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 606, "prefab": {"__uuid__": "2311e585-07bc-4508-9ee8-16eac7f3a2eb", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 96, "prefab": {"__uuid__": "70bfd287-5045-4909-973b-bc4b833f35dc", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 507, "prefab": {"__uuid__": "165d5ee6-fb28-4e74-b2a5-31d2ebbb7d06", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 594, "prefab": {"__uuid__": "abc63970-6eb5-4689-8b80-ae3b117d1453", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 489, "prefab": {"__uuid__": "5095b748-1b34-4f45-a1ce-025478537500", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 1, "prefab": {"__uuid__": "001403b2-3ed9-4d75-8299-707d15686f56", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 795, "prefab": {"__uuid__": "b96c5afb-8d6f-4371-b293-7305f8569ec7", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 796, "prefab": {"__uuid__": "910fec71-2f82-4893-b53e-ced6fe2b3e19", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 558, "prefab": {"__uuid__": "b9fead85-7647-4080-92c4-16508144b5fc", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 749, "prefab": {"__uuid__": "1a2d6c8f-021a-4532-92a8-74a5e60d799f", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 71, "prefab": {"__uuid__": "013e4697-3212-45e2-8fa5-0db67f0852cf", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 2, "prefab": {"__uuid__": "c50e0b57-d46a-4030-88d5-65b899116c15", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 673, "prefab": {"__uuid__": "bd05ef56-0ca8-43b7-a3fc-efd0791ee455", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 679, "prefab": {"__uuid__": "be2341f4-80b4-4217-904b-42c8ab74d705", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 752, "prefab": {"__uuid__": "209e74bf-206e-42b5-bb48-1262cf153bca", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 6799, "prefab": {"__uuid__": "85c932d0-220e-44be-9cd8-69eb8473db29", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 636, "prefab": {"__uuid__": "1affe361-1610-4c0f-a09e-52593e74db6d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 742, "prefab": {"__uuid__": "fb431cc9-6296-48b5-855b-3d99a6b4c01d", "__expectedType__": "cc.Prefab"}}, {"__type__": "ObjectPrefabMapping", "shapeID": 306, "prefab": {"__uuid__": "54daa05f-8f25-4913-a460-09cdcc23c263", "__expectedType__": "cc.Prefab"}}, {"__type__": "cc.Node", "_name": "Boxes", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 167}, {"__id__": 168}, {"__id__": 169}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 198.537, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "48luLa6G5KuL2Vyk1gvPh+"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "f5QbriNqVCd6/qbheNnnyG"}, {"__type__": "9c048K0YnZNh6oLHMOsa5fI", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": null, "boxPrefab": {"__uuid__": "485371f2-1e8a-404b-ae2c-a20f851b6f3e", "__expectedType__": "cc.Prefab"}, "_id": "ebo8XR6lFGwZb5NaSzOxYu"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": null, "_alignFlags": 40, "_target": null, "_left": 0, "_right": 0, "_top": 909.9999999999999, "_bottom": 909.9999999999999, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1304, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "2994AZGN9L3Zn7++ThDdPY"}, {"__type__": "cc.Node", "_name": "W<PERSON>Manager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 10}, "_children": [], "_active": true, "_components": [{"__id__": 171}, {"__id__": 172}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "85i7QBqhlIpL42AqFTmdA7"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "a812MJTHJBC55xlKfyg5Nj"}, {"__type__": "877adZi7lBL37frC9U5DRek", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 170}, "_enabled": true, "__prefab": null, "LinePrefab": {"__uuid__": "e2b3de03-5960-40f7-9f21-e01bfb256e1d", "__expectedType__": "cc.Prefab"}, "_id": "e1TSbrPKNM2buGQ0UViBE8"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1080, "height": 1920}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "b17vxK2hxHlb2lZKZ5NGan"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 10}, "_enabled": true, "__prefab": null, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 100, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "01UGRlyp9PdKW5QKHoFnXr"}, {"__type__": "cc.Node", "_name": "Boxes-001", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 176}, {"__id__": 177}, {"__id__": 178}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -192, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.5555555555555556, "y": 0.5555555555555556, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "d2ESj7DqtGoagpv22W0lmN"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 1695.6, "height": 80}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7bdGDMhaREb4UP1lulcE1x"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_alignFlags": 40, "_target": null, "_left": -300, "_right": -300, "_top": 909.9999999999999, "_bottom": 909.9999999999999, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 1304, "_originalHeight": 100, "_alignMode": 2, "_lockFlags": 0, "_id": "01nWUxn0hB6IujS3MG1xVp"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 175}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 223, "g": 217, "b": 189, "a": 255}, "_spriteFrame": {"__uuid__": "f1c9df4d-c0cb-4702-9fd1-be822fe37587@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "a6EovIlpdJl5d3n/BK4mI5"}, {"__type__": "cc.Node", "_name": "Picture", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 180}, {"__id__": 181}, {"__id__": 182}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2289P/x+ZA7aV1y6V/b303"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "ebwTtgs5VKQpc7II5gBQ9U"}, {"__type__": "a781epEW/JPcK6j+9xe09QE", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "pixelPrefab": {"__uuid__": "54c2ce62-4d46-4bbb-9e26-5ea61a27e5d4", "__expectedType__": "cc.Prefab"}, "_id": "767xazxIhJG5ok6IIAq3Ea"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 179}, "_enabled": true, "__prefab": null, "_resizeMode": 2, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_startAxis": 1, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 0, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "be8sanzXFIYKlb+4VCDdln"}, {"__type__": "cc.Node", "_name": "Sprite", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 185}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 0.1, "y": 0.1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "69xNpb5lBBmoP+h3KoCfbY"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 3200, "height": 3200}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "8cz69iwARA3Jzxo4Mj+Av9"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 192, "g": 143, "b": 81, "a": 65}, "_spriteFrame": {"__uuid__": "325b3446-24de-44df-8ccf-95da14c7246c@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 2, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "42sSi7FSxO7oEyJFJFeAcD"}, {"__type__": "cc.Node", "_name": "board_ingame", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 9}, "_children": [], "_active": true, "_components": [{"__id__": 187}, {"__id__": 188}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "496ioGA9xPB5ulD+EVdoyj"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 342, "height": 352}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "92VBQw9odACJNBMjV2v51t"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 186}, "_enabled": true, "__prefab": null, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "b0e1e4fe-e4f7-4826-9d8c-a97a9e504c22@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 1, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": "dbFho3GJNOCLfYZdKbwJGd"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 9}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 342, "height": 352}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "7d2/6PfE1LuqSnObkTmrdO"}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_contentSize": {"__type__": "cc.Size", "width": 320, "height": 320}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": "1bxU6OcMpDIosUBXdb0/Vv"}, {"__type__": "cc.Layout", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 8}, "_enabled": true, "__prefab": null, "_resizeMode": 2, "_layoutType": 3, "_cellSize": {"__type__": "cc.Size", "width": 10, "height": 10}, "_startAxis": 1, "_paddingLeft": 0, "_paddingRight": 0, "_paddingTop": 0, "_paddingBottom": 0, "_spacingX": 0, "_spacingY": 0, "_verticalDirection": 0, "_horizontalDirection": 0, "_constraint": 0, "_constraintNum": 2, "_affectedByScale": false, "_isAlign": false, "_id": "56VeiuCepIubboPKcms1fO"}, {"__type__": "c27265rTGdHhbHxwaU7RTWJ", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 193}, "_enabled": true, "__prefab": null, "data": {"__uuid__": "30ca483b-1731-4b25-abf6-1594b2f305ee", "__expectedType__": "cc.Json<PERSON>set"}, "levelData": [{"__id__": 194}, {"__id__": 197}], "_id": "4fe5NqoxNOwKMlAR6Lf/Qx"}, {"__type__": "cc.Node", "_name": "LevelData", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 192}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "2fW2R97clL75I2ptpb2dCp"}, {"__type__": "LevelDataConfig", "ColorShapes": [{"__type__": "cc.Color", "r": 184, "g": 85, "b": 202, "a": 255}], "ObjectDatas": [{"__id__": 195}], "BoxDatas": [{"__id__": 196}]}, {"__type__": "ObjectDataConfig", "Type": 733, "Position": {"__type__": "cc.Vec3", "x": 0, "y": -208, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 1, "Colors": [1, 1, 1]}, {"__type__": "BoxDataConfig", "colorType": 1, "slot": 3}, {"__type__": "LevelDataConfig", "ColorShapes": [{"__type__": "cc.Color", "r": 166, "g": 233, "b": 94, "a": 255}, {"__type__": "cc.Color", "r": 255, "g": 228, "b": 87, "a": 255}, {"__type__": "cc.Color", "r": 81, "g": 161, "b": 255, "a": 255}], "ObjectDatas": [{"__id__": 198}, {"__id__": 199}, {"__id__": 200}, {"__id__": 201}, {"__id__": 202}, {"__id__": 203}, {"__id__": 204}, {"__id__": 205}], "BoxDatas": [{"__id__": 206}, {"__id__": 207}, {"__id__": 208}, {"__id__": 209}, {"__id__": 210}, {"__id__": 211}]}, {"__type__": "ObjectDataConfig", "Type": 489, "Position": {"__type__": "cc.Vec3", "x": -220, "y": -122, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 1, "Colors": [5]}, {"__type__": "ObjectDataConfig", "Type": 489, "Position": {"__type__": "cc.Vec3", "x": 220, "y": -122, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 1, "Colors": [5]}, {"__type__": "ObjectDataConfig", "Type": 636, "Position": {"__type__": "cc.Vec3", "x": 220, "y": 88, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 2, "Colors": [8, 7]}, {"__type__": "ObjectDataConfig", "Type": 636, "Position": {"__type__": "cc.Vec3", "x": -220, "y": 88, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 2, "Colors": [13, 8]}, {"__type__": "ObjectDataConfig", "Type": 306, "Position": {"__type__": "cc.Vec3", "x": 220, "y": -328, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 2, "Colors": [13, 7, 5]}, {"__type__": "ObjectDataConfig", "Type": 306, "Position": {"__type__": "cc.Vec3", "x": -220, "y": -328, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 2, "Colors": [7, 8, 13]}, {"__type__": "ObjectDataConfig", "Type": 742, "Position": {"__type__": "cc.Vec3", "x": 220, "y": -370, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 3, "Colors": [5, 4, 4]}, {"__type__": "ObjectDataConfig", "Type": 742, "Position": {"__type__": "cc.Vec3", "x": -220, "y": -370, "z": 0}, "Rotation": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "layer": 3, "Colors": [4, 5, 5]}, {"__type__": "BoxDataConfig", "colorType": 5, "slot": 3}, {"__type__": "BoxDataConfig", "colorType": 13, "slot": 3}, {"__type__": "BoxDataConfig", "colorType": 7, "slot": 3}, {"__type__": "BoxDataConfig", "colorType": 8, "slot": 3}, {"__type__": "BoxDataConfig", "colorType": 4, "slot": 3}, {"__type__": "BoxDataConfig", "colorType": 5, "slot": 3}, {"__type__": "cc.Node", "_name": "SoundManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 213}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "434nCQuy9KfpyKmP5/s5Hg"}, {"__type__": "e52aevAlwVBV5WscPFKhj6R", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 212}, "_enabled": true, "__prefab": null, "BGM": {"__uuid__": "af1ea2ae-cee9-480e-87b8-e5af6362e3a8", "__expectedType__": "cc.AudioClip"}, "SelectBolt": {"__uuid__": "510bed42-c22d-4146-810e-197efceef024", "__expectedType__": "cc.AudioClip"}, "NextBox": {"__uuid__": "df8e8044-ee87-47ab-b4ec-97ecb847636a", "__expectedType__": "cc.AudioClip"}, "RemoveBolt": {"__uuid__": "ec34e5bf-164c-4ac0-923c-a8e099ea0ee2", "__expectedType__": "cc.AudioClip"}, "Knitt": {"__uuid__": "fcffea8e-cc47-4b46-887c-7eb99cac13a5", "__expectedType__": "cc.AudioClip"}, "Wrap": {"__uuid__": "31a366d2-1b3a-482f-a379-92fa3ee9adbf", "__expectedType__": "cc.AudioClip"}, "Complete_Level": {"__uuid__": "41105425-1f84-4d96-b317-b76cc8204344", "__expectedType__": "cc.AudioClip"}, "Lose": {"__uuid__": "48ce750a-00b2-4427-a45a-a26d554eee52", "__expectedType__": "cc.AudioClip"}, "_id": "66I5dfQCBBbbNOzNKc8dsH"}, {"__type__": "cc.Node", "_name": "FlowManager", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 4}, "_children": [], "_active": true, "_components": [{"__id__": 215}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "20zz/omtZM0b5dPfxkTo3t"}, {"__type__": "76883JienZG3JGhO/CUgb9W", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 214}, "_enabled": true, "__prefab": null, "_id": "1cTiL4xAJO5agz1M7Gccdv"}, {"__type__": "cc.Node", "_name": "ColorHolder", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [], "_active": true, "_components": [{"__id__": 217}], "_prefab": null, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": "b6K3Bi3QdBpK11B8CbKo3i"}, {"__type__": "59604HATMNKwrv8AWxqQKD4", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 216}, "_enabled": true, "__prefab": null, "boltMappings": [{"__id__": 218}, {"__id__": 219}, {"__id__": 220}, {"__id__": 221}, {"__id__": 222}, {"__id__": 223}, {"__id__": 224}, {"__id__": 225}, {"__id__": 226}, {"__id__": 227}, {"__id__": 228}, {"__id__": 229}, {"__id__": 230}, {"__id__": 231}, {"__id__": 232}, {"__id__": 233}], "boxMappings": [{"__id__": 234}, {"__id__": 235}, {"__id__": 236}, {"__id__": 237}, {"__id__": 238}, {"__id__": 239}, {"__id__": 240}, {"__id__": 241}, {"__id__": 242}, {"__id__": 243}, {"__id__": 244}, {"__id__": 245}, {"__id__": 246}, {"__id__": 247}, {"__id__": 248}, {"__id__": 249}], "boxLidMappings": [{"__id__": 250}, {"__id__": 251}, {"__id__": 252}, {"__id__": 253}, {"__id__": 254}, {"__id__": 255}, {"__id__": 256}, {"__id__": 257}, {"__id__": 258}, {"__id__": 259}, {"__id__": 260}, {"__id__": 261}, {"__id__": 262}, {"__id__": 263}, {"__id__": 264}, {"__id__": 265}], "_id": "e96Y3Ne7BM/aGwfXCPZqe1"}, {"__type__": "BoltMapping", "colorType": 1, "spriteFrame": {"__uuid__": "42a0f5ed-0686-44e6-bdd8-0f79c0ee7fcd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 2, "spriteFrame": {"__uuid__": "9da01fb0-8547-4009-9d70-8bfc915d2e0d@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 3, "spriteFrame": {"__uuid__": "bd508f27-f861-4dc4-9688-31be845e32bd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 4, "spriteFrame": {"__uuid__": "b88a5a9a-9295-437d-9f32-cd6172ec6565@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 5, "spriteFrame": {"__uuid__": "f78ddd2c-537d-41bf-bcaa-c117fdf5c92e@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 6, "spriteFrame": {"__uuid__": "92f73b9d-2ee3-42ba-920c-af1f064bcffa@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 7, "spriteFrame": {"__uuid__": "668ed23c-02d6-41aa-98a4-1a75dd0891fd@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 8, "spriteFrame": {"__uuid__": "56b54483-59f8-470e-9e6c-462833f22cca@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 9, "spriteFrame": {"__uuid__": "f181233e-93e7-48a0-a532-8be601a998d7@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 10, "spriteFrame": {"__uuid__": "06930152-466b-439f-8a62-11aff1d8c881@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 11, "spriteFrame": {"__uuid__": "b88a5a9a-9295-437d-9f32-cd6172ec6565@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 12, "spriteFrame": {"__uuid__": "f66f0608-f77c-4e24-8366-822b67d1cb89@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 14, "spriteFrame": {"__uuid__": "9ef408ff-6f5b-40f3-9659-e255ace5804f@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 13, "spriteFrame": {"__uuid__": "9df07aea-9a99-4b1b-8477-1d00aa2ebd1b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 15, "spriteFrame": {"__uuid__": "f13edac8-5f8f-4e66-b262-f1e8ad53b271@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoltMapping", "colorType": 16, "spriteFrame": {"__uuid__": "be0c8ce2-ba60-4760-a45f-f35a00f22f09@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 1, "spriteFrame": {"__uuid__": "2521e052-a74c-4323-a5fe-7b1712e2d9e7@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 2, "spriteFrame": {"__uuid__": "8c657dc5-9d13-4a8e-9c34-59830362681a@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 3, "spriteFrame": {"__uuid__": "eede35ea-3d96-4fac-8d94-4abf09a19df2@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 4, "spriteFrame": {"__uuid__": "b5411a72-2420-4151-9c08-adef18f24a8d@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 5, "spriteFrame": {"__uuid__": "40433836-d513-4e43-bf87-49bcda12eb64@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 6, "spriteFrame": {"__uuid__": "5e7f1a46-3c2b-49bf-b905-d0bb47d80d55@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 7, "spriteFrame": {"__uuid__": "07882cca-fea3-4fcc-a356-922f422c760c@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 8, "spriteFrame": {"__uuid__": "9283bcb5-a07b-45f9-9ee1-1a69c315274a@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 9, "spriteFrame": {"__uuid__": "976ebc4e-762b-45d2-8c60-e7994900a1b0@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 10, "spriteFrame": {"__uuid__": "44ffb5dd-d7da-44d3-96b1-df117ea1e3d0@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 11, "spriteFrame": {"__uuid__": "13148da0-22ab-4747-97ff-102dc981fff0@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 12, "spriteFrame": {"__uuid__": "1a598ec3-aedd-4d7c-bd27-26fa28971b77@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 14, "spriteFrame": {"__uuid__": "c461c423-3b53-42b9-9de5-0d556bff2718@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 13, "spriteFrame": {"__uuid__": "6957eea6-ea44-471b-a4c2-1ec12db5849e@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 15, "spriteFrame": {"__uuid__": "97a94ab7-f584-4660-b602-f717df999008@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxMapping", "colorType": 16, "spriteFrame": {"__uuid__": "4b5841f9-cbf7-44c2-b170-67e327149242@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 1, "spriteFrame": {"__uuid__": "dd5af59c-3893-4cc2-bf4f-ef2334de0c4f@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 2, "spriteFrame": {"__uuid__": "f8efb1a6-2504-4d22-bfd5-b16c5f64da14@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 3, "spriteFrame": {"__uuid__": "27d96635-ba80-40d2-9d70-1d77f1269504@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 4, "spriteFrame": {"__uuid__": "a0ad2383-ff8e-4ff4-8b5c-885a607248aa@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 5, "spriteFrame": {"__uuid__": "6509fa37-0583-4395-bf0d-232c8e187f9b@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 6, "spriteFrame": {"__uuid__": "f28e8da6-0f0a-481a-a12c-06d56e43519e@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 7, "spriteFrame": {"__uuid__": "ebf96f6b-0a1a-431b-b278-4a77873c847f@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 8, "spriteFrame": {"__uuid__": "7ddefa28-c575-4d89-acab-0300f44fd6db@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 9, "spriteFrame": {"__uuid__": "*************-4ff4-8bd2-8259f44a2e3c@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 10, "spriteFrame": {"__uuid__": "cac019ce-c86a-484d-b2fb-d56c98343862@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 11, "spriteFrame": {"__uuid__": "89d115af-dba8-40d3-8725-ce67542e6dec@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 12, "spriteFrame": {"__uuid__": "79c76304-5212-4ff4-8ec9-a0f2a7edaa73@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 14, "spriteFrame": {"__uuid__": "16b85411-0102-4232-b3dd-998dc9fc3541@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 13, "spriteFrame": {"__uuid__": "756645a3-fd49-43bb-bba1-7ac35e1c6abc@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 15, "spriteFrame": {"__uuid__": "c36ea381-e439-4b03-af96-3b9b57546d87@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "BoxLidMapping", "colorType": 16, "spriteFrame": {"__uuid__": "7ad29f93-e89e-4917-91ca-98daa4cec195@f9941", "__expectedType__": "cc.SpriteFrame"}}, {"__type__": "cc.PrefabInfo", "root": null, "asset": null, "fileId": "9025198a-0f7a-420d-adae-adc14e5d6a00", "instance": null, "targetOverrides": [{"__id__": 267}, {"__id__": 269}], "nestedPrefabInstanceRoots": [{"__id__": 43}, {"__id__": 60}, {"__id__": 72}]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 6}, "sourceInfo": null, "propertyPath": ["remarkable"], "target": {"__id__": 43}, "targetInfo": {"__id__": 268}}, {"__type__": "cc.TargetInfo", "localID": ["80ndhn29pB2LGTRPT6tSII"]}, {"__type__": "cc.TargetOverrideInfo", "source": {"__id__": 6}, "sourceInfo": null, "propertyPath": ["labelAnim"], "target": {"__id__": 60}, "targetInfo": {"__id__": 270}}, {"__type__": "cc.TargetInfo", "localID": ["4clMgTB3pBHo2w92dh2QlS"]}, {"__type__": "cc.SceneGlobals", "ambient": {"__id__": 272}, "shadows": {"__id__": 273}, "_skybox": {"__id__": 274}, "fog": {"__id__": 275}, "octree": {"__id__": 276}, "skin": {"__id__": 277}, "lightProbeInfo": {"__id__": 278}, "postSettings": {"__id__": 279}, "bakedWithStationaryMainLight": false, "bakedWithHighpLightmap": false}, {"__type__": "cc.AmbientInfo", "_skyColorHDR": {"__type__": "cc.Vec4", "x": 0.7803921568627451, "y": 0.8235294117647058, "z": 0.8666666666666667, "w": 0.520833125}, "_skyColor": {"__type__": "cc.Vec4", "x": 0.7803921568627451, "y": 0.8235294117647058, "z": 0.8666666666666667, "w": 0.520833125}, "_skyIllumHDR": 20000, "_skyIllum": 20000, "_groundAlbedoHDR": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_groundAlbedo": {"__type__": "cc.Vec4", "x": 0.2, "y": 0.2, "z": 0.2, "w": 1}, "_skyColorLDR": {"__type__": "cc.Vec4", "x": 0.452588, "y": 0.607642, "z": 0.755699, "w": 0}, "_skyIllumLDR": 0.8, "_groundAlbedoLDR": {"__type__": "cc.Vec4", "x": 0.618555, "y": 0.577848, "z": 0.544564, "w": 0}}, {"__type__": "cc.ShadowsInfo", "_enabled": false, "_type": 0, "_normal": {"__type__": "cc.Vec3", "x": 0, "y": 1, "z": 0}, "_distance": 0, "_planeBias": 1, "_shadowColor": {"__type__": "cc.Color", "r": 76, "g": 76, "b": 76, "a": 255}, "_maxReceived": 4, "_size": {"__type__": "cc.Vec2", "x": 1024, "y": 1024}}, {"__type__": "cc.SkyboxInfo", "_envLightingType": 0, "_envmapHDR": null, "_envmap": null, "_envmapLDR": null, "_diffuseMapHDR": null, "_diffuseMapLDR": null, "_enabled": false, "_useHDR": true, "_editableMaterial": null, "_reflectionHDR": null, "_reflectionLDR": null, "_rotationAngle": 0}, {"__type__": "cc.FogInfo", "_type": 0, "_fogColor": {"__type__": "cc.Color", "r": 200, "g": 200, "b": 200, "a": 255}, "_enabled": false, "_fogDensity": 0.3, "_fogStart": 0.5, "_fogEnd": 300, "_fogAtten": 5, "_fogTop": 1.5, "_fogRange": 1.2, "_accurate": false}, {"__type__": "cc.OctreeInfo", "_enabled": false, "_minPos": {"__type__": "cc.Vec3", "x": -1024, "y": -1024, "z": -1024}, "_maxPos": {"__type__": "cc.Vec3", "x": 1024, "y": 1024, "z": 1024}, "_depth": 8}, {"__type__": "cc.SkinInfo", "_enabled": true, "_blurRadius": 0.01, "_sssIntensity": 3}, {"__type__": "cc.LightProbeInfo", "_giScale": 1, "_giSamples": 1024, "_bounces": 2, "_reduceRinging": 0, "_showProbe": true, "_showWireframe": true, "_showConvex": false, "_data": null, "_lightProbeSphereVolume": 1}, {"__type__": "cc.PostSettingsInfo", "_toneMappingType": 0}]