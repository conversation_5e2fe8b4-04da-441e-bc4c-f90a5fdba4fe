import { _decorator, CCBoolean, Component, instantiate, JsonAsset, Node, Prefab, Sprite, Color, Vec2, Vec3, UITransform } from 'cc';
import { ObjectManager } from './ObjectManager';
import { LevelData } from '../app/LevelData';
import { HoleManager } from './HoleManager';
import { BoxManager } from './BoxManager';
import { ColorType } from '../enums/Enums';
import { GameManager } from '../app/GameManager';
import { GameState } from '../enums/GameState';
import { Tutorial } from '../app/Tutorial';
import { ObjectComponent } from './Components/ObjectComponent';
import { Bolt } from './Components/Bolt';
import { Picture } from './Components/Picture';
import { TutorialSystem } from '../app/TutorialSystem';
const { ccclass, property } = _decorator;

@ccclass('BoardController')
export class BoardController extends Component {
    @property(Prefab)
    public pixelPrefab: Prefab = null;

    @property(JsonAsset)
    public tut: JsonAsset = null;
    @property(JsonAsset)
    public level: JsonAsset = null;

    private boardData: JsonAsset = null;

    @property(ObjectManager)
    public objectManager: ObjectManager = null;

    @property(HoleManager)
    public holeManager: HoleManager = null;

    @property(BoxManager)
    public boxManager: BoxManager = null;


    @property(JsonAsset)
    public pictureData: JsonAsset = null;

    @property(LevelData)
    public levelData: LevelData = null;

    @property(Picture)
    public picture: Picture = null;

    @property(Prefab)
    public tutorialPrefab: Prefab = null;


    @property(TutorialSystem)
    public tutorialSystem: TutorialSystem = null;

    private boardSize: number = 32;

    private board: Node[][] = [];

    private arrColorTut: ColorType[] = [ColorType.PastelGreen];
    private arrColor: ColorType[] = [ColorType.PastelLavender, ColorType.YellowGreen, ColorType.Yellow, ColorType.Magenta, ColorType.Yellow, ColorType.Red, ColorType.YellowGreen];
    private colorMapping: Map<string, Vec2[]> = new Map();

    public isPart: boolean = false;

    public tutorial: Tutorial = null;

    protected onLoad(): void {
        GameManager.Instance.onStateChange.on(this.onStateChangeCallback, this);
    }

    private onStateChangeCallback(state: GameState): void {
        // console.log('onStateChangeCallback', state);

        switch (state) {
            case GameState.Tutorial:

                

                break;

            case GameState.Prepare:
                this.isPart = true;
                this.boardData = this.level;
                this.arrColor = this.levelData.levelData[1].BoxDatas.map(box => box.colorType)
                this.initializeBoard();
                this.objectManager.initialize(this.levelData.levelData[1]);
                this.boxManager.initialize(this.levelData.levelData[1].BoxDatas);
                GameManager.Instance.setGameState(GameState.Playing);


                const currentBox = GameManager.Instance.boardController.boxManager.currentBox;
                const currentColor = currentBox.colorType;

                const arrBolts = GameManager.Instance.ObjectManager.bolts.filter(bolt => !bolt.isCollected && bolt.colorType === currentColor && !bolt.isOverlappingWithAnyCollider());
                if(arrBolts.length > 0) {
                    const firstBolt = arrBolts[0];
                    console.log('firstBolt', firstBolt.node.worldPosition);
                    this.tutorialSystem.playAnimFirstStep(firstBolt.node.worldPosition);
                }

                

                

                break;

            case GameState.Picture:
                this.picture.initializeBoard();
                this.picture.previewPicture(this.pictureData);
                // console.log(this.levelData.levelData);
                GameManager.Instance.setGameState(GameState.InitData);

                this.objectManager.initialize(this.levelData.levelData[1]);
                this.boxManager.initialize(this.levelData.levelData[1].BoxDatas);

                break

            default:
                break;
        }


    }

    public getColor(colorType: ColorType): Color {

        const colorInfo = this.boardData.json.colorInfos.find((c: any) => c.id === colorType);
        if (!colorInfo) return;

        // Tạo đối tượng Color
        const color = new Color(
            colorInfo.color.r * 255,
            colorInfo.color.g * 255,
            colorInfo.color.b * 255,
            colorInfo.color.a * 255
        );

        return color;
    }

    private initializeBoard() {

        this.node.removeAllChildren();

        this.board = [];
        for (let x = 0; x < this.boardSize; x++) {
            this.board[x] = [];
            for (let y = 0; y < this.boardSize; y++) {
                const pixel = instantiate(this.pixelPrefab);
                pixel.setParent(this.node);
                this.board[x][y] = pixel;
            }
        }

        this.colorMapping = this.initializeColor();


        const colorAutoFills = this.getColorAutoFill();
        if(colorAutoFills) {
            for(const colorAutoFill of colorAutoFills) {
                const {color, arrPos} = colorAutoFill;
                for(const pos of arrPos) {
                    const pixel = this.board[pos.x][pos.y];
                    if(pixel) {
                        pixel.getComponent(Sprite).color = color;
                    }
                }
            }
        }
    }

    public getSprite(pos: Vec2): Sprite {
        const pixel = this.board[pos.x][pos.y];
        if (pixel) {
            return pixel.getComponent(Sprite);
        }

        return null;
    }

    private getColorAutoFill(): {color: Color, arrPos: Vec2[]}[] {
        const colorMapping = new Map<string, Vec2[]>();
        const levelData = this.boardData.json;
        
        // Get all colorInfos from levelData
        const allColorInfos = levelData.colorInfos;
        
        // Find colors that are not in arrColor
        const unusedColors = allColorInfos.filter(colorInfo => {
            const colorId = colorInfo.id;
            return this.arrColor.indexOf(colorId) === -1;
        });

        if (unusedColors.length === 0) {
            return null;
        }

        // Map each unused color to its color and positions
        return unusedColors.map(selectedColor => {
            // Find positions for this color
            const positionInfo = levelData.positionInfos.find(p => String(p.colorID) === String(selectedColor.id));
            if (!positionInfo) {
                return null;
            }

            // Convert positions to Vec2 array
            const positions = positionInfo.positions.map(pos => new Vec2(pos.x, pos.y));

            // Create Color object
            const color = new Color(
                selectedColor.color.r * 255,
                selectedColor.color.g * 255,
                selectedColor.color.b * 255,
                selectedColor.color.a * 255
            );

            return { color, arrPos: positions };
        }).filter(result => result !== null);
    }

    private initializeColor() {
        const colorMapping = new Map<string, Vec2[]>();
        const levelData = this.boardData.json;
        if (!levelData) return colorMapping;

        const { positionInfos } = levelData;

        // Nếu là tutorial mode, không cần chia nhỏ positions

        const arrColor = this.isPart ? this.arrColor : this.arrColorTut;

        if (!this.isPart) {
            for (const colorType of arrColor) {
                const key = String(colorType);
                const positionInfo = positionInfos.find(p => String(p.colorID) === key);
                if (!positionInfo) continue;

                const positions = positionInfo.positions.map(pos => new Vec2(pos.x, pos.y));
                colorMapping.set(`${key}_0`, positions);
            }

        }

        // Đếm số lần xuất hiện của từng colorType trong arrColor
        const colorCount = new Map<string, number>();
        for (const colorType of arrColor) {
            const key = String(colorType);
            colorCount.set(key, (colorCount.get(key) || 0) + 1);
        }

        // Đếm số lần đã gán cho từng colorType
        const colorAssigned = new Map<string, number>();
        for (const colorType of arrColor) {
            const key = String(colorType);
            const total = colorCount.get(key) || 1;
            const assigned = colorAssigned.get(key) || 0;

            const positionInfo = positionInfos.find(p => String(p.colorID) === key);
            if (!positionInfo) continue;

            const positions = positionInfo.positions.map(pos => new Vec2(pos.x, pos.y));
            const chunkSize = Math.ceil(positions.length / total);
            const start = assigned * chunkSize;
            const end = Math.min(start + chunkSize, positions.length);

            colorMapping.set(`${key}_${assigned}`, positions.slice(start, end));
            colorAssigned.set(key, assigned + 1);
        }

        return colorMapping;
    }


    public getArrPos(key: string): { color: Color, arrPos: Vec3[], arrPos2: Vec2[] } {
        const color = Number(key.split('_')[0]);
        const levelData = this.boardData.json;





        const colorInfo = levelData.colorInfos.find((c: any) => c.id === color);

        const positions = this.colorMapping.get(key);

        const arrPos: Vec3[] = [];

        for (const pos of positions) {
            const x = pos.x;
            const y = pos.y;
            if (
                this.board &&
                this.board[x] &&
                this.board[x][y]
            ) {
                const pixel = this.board[x][y];
                const worldPos = pixel.getWorldPosition();
                arrPos.push(worldPos);
            }
        }

        return { color: colorInfo.color, arrPos: arrPos, arrPos2: positions };
    }

    public getDataString(arrColor: ColorType[]): string[] {
        const result: string[] = [];
        const colorCounts: Map<ColorType, number> = new Map();

        for (const color of arrColor) {
            const count = colorCounts.get(color) || 0;
            result.push(`${color}_${count}`);
            colorCounts.set(color, count + 1);
        }

        return result;
    }
}


