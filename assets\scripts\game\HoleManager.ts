import { _decorator, Component, Node } from 'cc';
import { Hole } from './Components/Hole';
import { Bolt } from './Components/Bolt';
import { GameManager } from '../app/GameManager';
import { RopeManager } from '../app/RopeManager';
import { SoundManager } from '../app/SoundManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('HoleManager')
export class HoleManager extends Component {

    private _holes: Hole[] = [];

    public get holes(): Hole[] {
        return this._holes;
    }

    private _isBusy: boolean = false;

    private _countBoltProcess: number = 0;

    public getFirstEmptyHole(): Hole | null {
        return this._holes.find(hole => !hole.isOccupied) || null;
    }

    protected onLoad(): void {
        this._holes = this.node.children.map(child => child.getComponent(Hole));
    }

    public setBolt(bolt: Bolt): { hole: Hole, flag: boolean } | null {
        const hole = this.getFirstEmptyHole();
        if (!hole) return { hole: null, flag: false };

        hole.setBolt(bolt);
        return { hole, flag: true };
    }

    public Lose(): boolean {

        if (this._isBusy) return false;
        if (this.getBoltFormHole().length > 0) return false;
        if (GameManager.Instance.getGameState() === GameState.DrawBoard || GameManager.Instance.getGameState() == GameState.FillBox) return false;
        return this._holes.every(hole => hole.isOccupied);
    }

    public getBoltFormHole(): Bolt[] {
        const currentBox = GameManager.Instance.boardController.boxManager.currentBox;
        if (!currentBox) return [];
        let count = 0;
        const bolts: Bolt[] = [];

        for (const hole of this._holes) {
            if (hole.isOccupied && hole.bolt && count < currentBox.lidCount) {

                const currentBox = GameManager.Instance.boardController.boxManager.currentBox;

                if (currentBox.checkCorrect(hole.bolt.colorType)) {
                    // this._isBusy = true;
                    GameManager.Instance.setGameState(GameState.FillBox);

                    const color = GameManager.Instance.boardController.getColor(hole.bolt.colorType);
                    const rope = RopeManager.Instance.getRope();
                    const startPos = hole.node.getWorldPosition().clone();

                    const arrPos = currentBox.getBoxLid().getPosPerBoxLid2();
                    const endPos = currentBox.getBoxLid().getPosPerBoxLid();

                    rope.createLine2(startPos, arrPos, endPos, color, 20, () => {
                        currentBox.checkCompleteLid();
                        // this._isBusy = false;
                        GameManager.Instance.setGameState(GameState.Playing);
                    }, (pos) => {

                    });

                    currentBox.getBoxLid().playAnimationAppear();
                    hole.bolt.playAnimationCollect(() => {
                        hole.clearBolt();

                        SoundManager.Instance.playSfx(SoundManager.Instance.RemoveBolt);


                    });

                    currentBox.IncreaseLidCount();

                    bolts.push(hole.bolt);
                    count++;

                }
            }
            if (count >= currentBox.lidCount) break;
        }
        return bolts;
    }

    public resetCountBoltProcess(): void {
        this._countBoltProcess = 0;
    }
}


