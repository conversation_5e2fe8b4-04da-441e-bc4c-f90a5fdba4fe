import { _decorator, Component, Node, tween, Vec3 } from 'cc';
import { LightFx } from './LightFx';
const { ccclass, property } = _decorator;

@ccclass('ShakeCTA')
export class ShakeCTA extends Component {
    @property(Node)
    private ctaNode: Node = null;

    @property
    private scaleAmount: number = 1.1;

    @property
    private duration: number = 0.5;

    @property
    private pauseDuration: number = 1.0;

    @property
    private rotateAmount: number = 3;

    @property(LightFx)
    private lightFx: LightFx = null;


    start() {
        this.playScaleAnimation();
    }

    private playScaleAnimation() {
        if (!this.ctaNode) return;

        // Stop any existing animations
        tween(this.ctaNode).stop();

        // Create infinite scale animation
        tween(this.ctaNode)
            .repeatForever(
                tween()
                    .to(this.duration,
                        { scale: new Vec3(this.scaleAmount, this.scaleAmount, this.scaleAmount) },
                        { easing: 'sineOut' })
                    .to(this.duration,
                        { scale: new Vec3(1, 1, 1) },
                        { easing: 'sineIn' })
                    .delay(this.pauseDuration)
            )
            .start();

        tween(this.ctaNode)
            .repeatForever(
                tween()
                    .to(this.duration / 2,
                        { angle: this.rotateAmount })
                    .call(() => {
                        if (this.lightFx) {
                            this.lightFx.playFxLight(this.duration);
                        }
                    })
                    .to(this.duration,
                        { angle: -this.rotateAmount })

                    .to(this.duration / 2,
                        { angle: 0 })
                    .delay(this.pauseDuration)
            )
            .start();


    }

    onDestroy() {
        // Stop animation when component is destroyed
        if (this.ctaNode) {
            tween(this.ctaNode).stop();
        }
    }
}


