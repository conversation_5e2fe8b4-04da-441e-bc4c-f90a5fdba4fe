import { _decorator, Component, Node, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ShakeCTA')
export class ShakeCTA extends Component {
    @property(Node)
    private ctaNode: Node = null;

    @property
    private shakeIntensity: number = 10;

    @property
    private duration: number = 0.1;

    @property
    private pauseDuration: number = 1.0;

    @property
    private useRandomDirection: boolean = true;

    private originalPosition: Vec3 = new Vec3();

    start() {
        this.playShakeAnimation();
    }

    private playShakeAnimation() {
        if (!this.ctaNode) return;

        // Store original position
        this.originalPosition = this.ctaNode.position.clone();

        // Stop any existing animations
        tween(this.ctaNode).stop();

        // Create infinite shake animation
        this.createShakeSequence();
    }

    private createShakeSequence() {
        const shakeSequence = tween();

        // Create multiple shake movements
        for (let i = 0; i < 6; i++) {
            const offsetX = this.useRandomDirection ?
                (Math.random() - 0.5) * 2 * this.shakeIntensity :
                (i % 2 === 0 ? this.shakeIntensity : -this.shakeIntensity);
            const offsetY = this.useRandomDirection ?
                (Math.random() - 0.5) * 2 * this.shakeIntensity :
                0;

            shakeSequence.to(this.duration, {
                position: new Vec3(
                    this.originalPosition.x + offsetX,
                    this.originalPosition.y + offsetY,
                    this.originalPosition.z
                )
            }, { easing: 'sineOut' });
        }

        // Return to original position
        shakeSequence.to(this.duration, {
            position: this.originalPosition.clone()
        }, { easing: 'sineOut' });

        // Add pause before next shake
        shakeSequence.delay(this.pauseDuration);

        // Start infinite shake animation
        tween(this.ctaNode)
            .repeatForever(shakeSequence)
            .start();
    }

    onDestroy() {
        // Stop animation when component is destroyed
        if (this.ctaNode) {
            tween(this.ctaNode).stop();
            // Reset to original position
            this.ctaNode.position = this.originalPosition;
        }
    }
}


