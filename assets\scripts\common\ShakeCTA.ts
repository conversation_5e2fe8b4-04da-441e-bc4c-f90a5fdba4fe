import { _decorator, Component, Node, tween, Vec3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ShakeCTA')
export class ShakeCTA extends Component {
    @property(Node)
    private ctaNode: Node = null;

    @property
    private scaleAmount: number = 1.1;

    @property
    private duration: number = 0.5;

    @property
    private pauseDuration: number = 1.0;

    @property
    private rotateAmount: number = 3;


    start() {
        this.playScaleAnimation();
    }

    private playScaleAnimation() {
        if (!this.ctaNode) return;

        // Stop any existing animations
        tween(this.ctaNode).stop();

        // Create combined scale and rotation animation
        tween(this.ctaNode)
            .repeatForever(
                tween()
                    .to(this.duration / 2,
                        {
                            scale: new Vec3(this.scaleAmount, this.scaleAmount, this.scaleAmount),
                            angle: this.rotateAmount
                        },
                        { easing: 'sineOut' })
                    .to(this.duration,
                        {
                            scale: new Vec3(1, 1, 1),
                            angle: -this.rotateAmount
                        },
                        { easing: 'sineIn' })
                    .to(this.duration / 2,
                        {
                            scale: new Vec3(this.scaleAmount, this.scaleAmount, this.scaleAmount),
                            angle: 0
                        },
                        { easing: 'sineOut' })
            )
            .start();
    }

    onDestroy() {
        // Stop animation when component is destroyed
        if (this.ctaNode) {
            tween(this.ctaNode).stop();
        }
    }
}


