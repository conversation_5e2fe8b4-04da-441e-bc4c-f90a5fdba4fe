import { _decorator, Component, Node } from 'cc';
import { Bolt } from './Bolt';
const { ccclass, property } = _decorator;

@ccclass('Hole')
export class Hole extends Component {
    private _isOccupied: boolean = false;
    private _bolt: Bolt | null = null;

    public get isOccupied(): boolean {
        return this._isOccupied;
    }

    public get bolt(): Bolt | null {
        return this._bolt;
    }

    public setBolt(bolt: Bolt): void {
        this._bolt = bolt;
        this._isOccupied = true;
    }

    public clearBolt(): void {
        this._bolt = null;
        this._isOccupied = false;
    }


}


