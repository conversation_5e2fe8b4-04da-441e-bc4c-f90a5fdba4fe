import { _decorator, Component, GraphicsComponent, Node, Vec3, Color, UITransform, tween, Vec2 } from 'cc';
import { RopeManager } from '../../app/RopeManager';
const { ccclass, property } = _decorator;

@ccclass('SimpleLineRender')
export class SimpleLineRender extends Component {
    @property(GraphicsComponent)
    line: GraphicsComponent = null;

    @property(Color)
    lineColor: Color = new Color(255, 255, 255, 255);

    @property
    lineWidth: number = 20;

    @property
    animationDuration: number = 0.5;

    @property
    fadeOutDuration: number = 0.3;

    @property
    waveAmplitude: number = 20; // Độ cao của sóng

    @property
    waveFrequency: number = 2; // Số lượng sóng

    @property
    waveSpeed: number = 15; // Tốc độ rung lắc

    private currentStart: Vec3 = new Vec3();
    private currentEnd: Vec3 = new Vec3();
    private isAnimating: boolean = false;
    private controlPoints: Vec3[] = [];
    private waveTime: number = 0;
    private onCompleteCallback: (() => void) | null = null;
    private onEndPointReachedCallback: ((worldPos: Vec2) => void) | null = null;

    public setAnimationDuration(duration: number): void {
        this.animationDuration = duration;
    }

    public createLine(start: Vec3, end: Vec3, color?: Color, width?: number, onComplete?: () => void) {
        this.line.clear();
        this.onCompleteCallback = onComplete || null;
        
        // Set line style
        this.line.lineWidth = width || this.lineWidth;
        this.line.strokeColor = color || this.lineColor;
        this.line.lineCap = GraphicsComponent.LineCap.ROUND;
        
        // Convert world positions to local positions
        const localStart = this.node.getComponent(UITransform).convertToNodeSpaceAR(start);
        const localEnd = this.node.getComponent(UITransform).convertToNodeSpaceAR(end);

        // Set initial positions
        this.currentStart.set(localStart);
        this.currentEnd.set(localStart);

        // Initialize control points
        this.initializeControlPoints(localStart, localEnd);

        // Start animation
        this.animateLine(localStart, localEnd);
    }

    public createLine2(startWorld: Vec3, boardPos: Vec2[], endPointsWorld: Vec3[], color?: Color, width?: number, onComplete?: () => void, onEndPointReached?: (worldPos: Vec2) => void) {
        if (this.isAnimating) {
            console.warn("SimpleLineRender is already animating. Current animation will be overridden.");
            // Optionally, stop the current tween if one is active and identifiable
        }
        if (!endPointsWorld || endPointsWorld.length === 0) {
            console.warn("endPointsWorld is empty or null.");
            if (onComplete) {
                onComplete();
            }
            return;
        }

        this.line.clear();
        this.onCompleteCallback = onComplete || null;
        this.onEndPointReachedCallback = onEndPointReached || null;
        this.isAnimating = true;

        // Set line style
        this.line.lineWidth = width || this.lineWidth;
        this.line.strokeColor = color || this.lineColor;
        this.line.lineCap = GraphicsComponent.LineCap.ROUND;

        const uiTransform = this.node.getComponent(UITransform);
        if (!uiTransform) {
            console.error("UITransform component not found on SimpleLineRender node.");
            this.isAnimating = false;
            if (this.onCompleteCallback) {
                this.onCompleteCallback();
                this.onCompleteCallback = null; // Clear callback after execution
                 // Assuming RopeManager is accessible and this instance should be returned to pool
                if (RopeManager && RopeManager.Instance) {
                    RopeManager.Instance.putRope(this);
                }
            }
            return;
        }

        const localStart = uiTransform.convertToNodeSpaceAR(startWorld);
        const localEndPoints = endPointsWorld.map(worldPos => uiTransform.convertToNodeSpaceAR(worldPos));

        this.currentStart.set(localStart);
        this.currentEnd.set(localStart); // Start with the line collapsed at localStart

        this.initializeControlPoints(localStart, localStart); // Initialize for a zero-length line

        // Calculate animation durations based on the total number of points
        let firstSegmentDuration: number;
        let subsequentSegmentDuration: number = 0.01; // Default for multi-segment, might be overwritten
        const numLocalEndPoints = localEndPoints.length; // Guaranteed to be >= 1 due to earlier check

        if (numLocalEndPoints === 1) {
            // If only one point, duration is min of animationDuration and 1s
            firstSegmentDuration = Math.min(this.animationDuration, 1.0);
            // subsequentSegmentDuration is not applicable here
        } else { // numLocalEndPoints > 1
            const initialFirstDuration = this.animationDuration;
            const initialSubsequentDuration = 0.01; // Default duration for points after the first

            // Calculate the total time it would take with default durations
            const calculatedTotalDuration = initialFirstDuration + (numLocalEndPoints - 1) * initialSubsequentDuration;

            if (calculatedTotalDuration <= 1.0) {
                // If total time is within 1s, use default durations
                firstSegmentDuration = initialFirstDuration;
                subsequentSegmentDuration = initialSubsequentDuration;
            } else {
                // Total duration exceeds 1s, so scale down to fit into 1s
                const targetTotalDuration = 1.0;
                const scalingFactor = targetTotalDuration / calculatedTotalDuration;
                firstSegmentDuration = initialFirstDuration * scalingFactor;
                subsequentSegmentDuration = initialSubsequentDuration * scalingFactor;
            }
        }

        // Create a tween sequence for the end point
        let endPointTweenChain = tween(this.currentEnd);

        // Animate to the first point using the calculated firstSegmentDuration
        const firstLocalEnd = localEndPoints[0];
        endPointTweenChain = endPointTweenChain.to(firstSegmentDuration, { x: firstLocalEnd.x, y: firstLocalEnd.y, z: firstLocalEnd.z }, {
            onUpdate: () => {
                this.updateControlPoints(this.currentStart, this.currentEnd);
                this.drawLine();
            }
        }).call(() => {
            if (this.onEndPointReachedCallback && endPointsWorld[0]) {
                    this.onEndPointReachedCallback(boardPos[0]);

                
            }
        });

        // Animate through the rest of the points with 0.02s interval
        for (let i = 1; i < localEndPoints.length; i++) {
            const targetLocalEnd = localEndPoints[i];
            const worldEndPoint = boardPos[i];
            endPointTweenChain = endPointTweenChain.to(subsequentSegmentDuration, { x: targetLocalEnd.x, y: targetLocalEnd.y, z: targetLocalEnd.z }, {
                onUpdate: () => {
                    this.updateControlPoints(this.currentStart, this.currentEnd);
                    this.drawLine();
                }
            }).call(() => {
                if (this.onEndPointReachedCallback && worldEndPoint) {
                    this.onEndPointReachedCallback(worldEndPoint);
                }
            });
        }

        // After all movements, call startFadeOut
        endPointTweenChain = endPointTweenChain.call(() => {
            // Ensure currentEnd is at the final target position before starting fade-out
            if (localEndPoints.length > 0) {
                const finalTarget = localEndPoints[localEndPoints.length -1];
                this.currentEnd.set(finalTarget);
                 this.updateControlPoints(this.currentStart, this.currentEnd);
                 this.drawLine();
            }
            this.startFadeOut(); // This method will handle isAnimating = false, onCompleteCallback, and putRope
        });

        endPointTweenChain.start();
    }

    private initializeControlPoints(start: Vec3, end: Vec3) {
        this.controlPoints = [];
        const numPoints = 10; // Số điểm điều khiển
        const direction = new Vec3(end.x - start.x, end.y - start.y, 0).normalize();
        const length = Vec3.distance(start, end);

        for (let i = 0; i <= numPoints; i++) {
            const t = i / numPoints;
            const point = new Vec3(
                start.x + direction.x * length * t,
                start.y + direction.y * length * t,
                0
            );
            this.controlPoints.push(point);
        }
    }

    private updateControlPoints(start: Vec3, end: Vec3) {
        const direction = new Vec3(end.x - start.x, end.y - start.y, 0).normalize();
        const perpendicular = new Vec3(-direction.y, direction.x, 0);
        const length = Vec3.distance(start, end);

        this.waveTime += this.waveSpeed * 0.016; // Assuming 60 FPS, so 0.016 is approx 1/60

        if (this.controlPoints.length === 0 && length > 0) { // Initialize control points if empty and line has length
            const numPoints = 20;
            for (let i = 0; i <= numPoints; i++) {
                this.controlPoints.push(new Vec3());
            }
        }

        for (let i = 0; i < this.controlPoints.length; i++) {
            const t = i / (this.controlPoints.length - 1);
            // Create a more dynamic wave
            const waveOffset = Math.sin(t * Math.PI * this.waveFrequency + this.waveTime) * this.waveAmplitude * (1 - Math.abs(0.5 - t) * 2);

            this.controlPoints[i].set(
                start.x + direction.x * length * t + perpendicular.x * waveOffset,
                start.y + direction.y * length * t + perpendicular.y * waveOffset,
                0
            );
        }
    }

    private drawLine() {
        this.line.clear();
        if (this.controlPoints.length < 2) return; // Need at least two points to draw

        this.line.moveTo(this.controlPoints[0].x, this.controlPoints[0].y);

        // Use bezierCurveTo for smoother curves
        for (let i = 1; i < this.controlPoints.length; i++) {
            this.line.lineTo(this.controlPoints[i].x, this.controlPoints[i].y);
        }
        
        this.line.stroke();
    }

    private animateLine(targetStart: Vec3, targetEnd: Vec3) {
        if (this.isAnimating) {
            return;
        }

        this.isAnimating = true;

        // Create tween for start point
        tween(this.currentStart)
            .to(this.animationDuration, targetStart, {
                onUpdate: () => {
                    this.updateControlPoints(this.currentStart, this.currentEnd);
                    this.drawLine();
                }
            })
            .start();

        // Create tween for end point
        tween(this.currentEnd)
            .to(this.animationDuration, targetEnd, {
                onUpdate: () => {
                    this.updateControlPoints(this.currentStart, this.currentEnd);
                    this.drawLine();
                },
                onComplete: () => {
                    this.startFadeOut();
                }
            })
            .start();
    }

    private startFadeOut() {
        tween(this.currentStart)
            .to(this.fadeOutDuration, this.currentEnd, {
                onUpdate: () => {
                    this.updateControlPoints(this.currentStart, this.currentEnd);
                    this.drawLine();
                },
                onComplete: () => {
                    this.line.clear();
                    this.isAnimating = false;
                    // console.log("End");
                    if (this.onCompleteCallback) {
                        this.onCompleteCallback();
                        this.onCompleteCallback = null;
                        RopeManager.Instance.putRope(this);
                    }
                    this.onEndPointReachedCallback = null;
                }
            })
            .start();
    }
}


