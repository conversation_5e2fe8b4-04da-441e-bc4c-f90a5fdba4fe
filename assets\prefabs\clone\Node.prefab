[{"__type__": "cc.Prefab", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_native": "", "data": {"__id__": 1}, "optimizationPolicy": 0, "persistent": false}, {"__type__": "cc.Node", "_name": "Node", "_objFlags": 0, "__editorExtras__": {}, "_parent": null, "_children": [{"__id__": 2}, {"__id__": 166}, {"__id__": 231}, {"__id__": 296}, {"__id__": 361}, {"__id__": 426}, {"__id__": 656}], "_active": true, "_components": [], "_prefab": {"__id__": 787}, "_lpos": {"__type__": "cc.Vec3", "x": -540, "y": -960, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 1073741824, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "28", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 3}, {"__id__": 19}, {"__id__": 25}, {"__id__": 60}, {"__id__": 93}, {"__id__": 126}], "_active": true, "_components": [{"__id__": 159}, {"__id__": 48}, {"__id__": 161}, {"__id__": 163}], "_prefab": {"__id__": 165}, "_lpos": {"__type__": "cc.Vec3", "x": 154, "y": 4297, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 4}], "_active": true, "_components": [{"__id__": 10}, {"__id__": 12}, {"__id__": 14}, {"__id__": 16}], "_prefab": {"__id__": 18}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 3}, "_children": [], "_active": true, "_components": [{"__id__": 5}, {"__id__": 7}], "_prefab": {"__id__": 9}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 6}, "_contentSize": {"__type__": "cc.Size", "width": 408, "height": 286}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b4WV+BMaBAAI98khmOIrlY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 4}, "_enabled": true, "__prefab": {"__id__": 8}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "22038e32-1807-4c27-8862-600cdc805e95@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88v2GKUxNERKt3a5nZg9YA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0uSkMeQxMrI8Bd0bBy5i1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 11}, "_contentSize": {"__type__": "cc.Size", "width": 408, "height": 286}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7bD2iCwpdAsbzT0pRA9Qjl"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 13}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3e3xeRm9NBub4Q1h+h7ll3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 15}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "22038e32-1807-4c27-8862-600cdc805e95@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "04aiunVQdMnYu/X2gNlEeA"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 3}, "_enabled": true, "__prefab": {"__id__": 17}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 408, "_originalHeight": 286, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "34SecP9TJBi48aYaj/4z/k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40x80nTn9DLpcBSek8qEu/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [], "_active": true, "_components": [{"__id__": 20}, {"__id__": 22}], "_prefab": {"__id__": 24}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 21}, "_contentSize": {"__type__": "cc.Size", "width": 408, "height": 286}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50aZ9/chpEk7CTS/BbL6KH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 19}, "_enabled": true, "__prefab": {"__id__": 23}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "22038e32-1807-4c27-8862-600cdc805e95@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71uKqalpdMALcHaxnuJSvF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "842hCC4PtA5rhzlfJYgS9+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 26}], "_active": true, "_components": [{"__id__": 42}, {"__id__": 44}, {"__id__": 46}, {"__id__": 50}, {"__id__": 53}, {"__id__": 55}, {"__id__": 57}], "_prefab": {"__id__": 59}, "_lpos": {"__type__": "cc.Vec3", "x": -126.812, "y": 66.479, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 25}, "_children": [{"__id__": 27}, {"__id__": 33}], "_active": true, "_components": [{"__id__": 39}], "_prefab": {"__id__": 41}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 28}, {"__id__": 30}], "_prefab": {"__id__": 32}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 29}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cj0QU/yFPBryuRxwCE7RQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 27}, "_enabled": true, "__prefab": {"__id__": 31}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "54Ntn72+FMhrXk2xvUSy8K"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "57cNcwmONArZyuSkpALNvh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 26}, "_children": [], "_active": true, "_components": [{"__id__": 34}, {"__id__": 36}], "_prefab": {"__id__": 38}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 35}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "97/ZRFYVFO1qc8KCzwM/DJ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 33}, "_enabled": true, "__prefab": {"__id__": 37}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fe+yvGLsNMh6ig9eaHtAJ+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "14hsekXLNCjo/aww4CBr4m", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 26}, "_enabled": true, "__prefab": {"__id__": 40}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37wzx4fqdFTISn1FajtfU+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10BQiV29VB/ZSSyiPgmGOf", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 43}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c78DkP13RMPr8hSwHmdBeQ"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 45}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a2HMAD4PpFlqQ8c96N9Zip"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 47}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -126.812, "y": 66.479}, "collideConnected": false, "connectedBody": {"__id__": 48}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffYd67npZGKoilLz/nRPc6"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 49}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "48137uBQlJwr8Ii9vfoDjc"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 51}, "clickEvents": [{"__id__": 52}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27Q8qTfqlJ/JCBdOFX+K0F"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 25}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 54}, "bolt": {"__id__": 36}, "thread": {"__id__": 30}, "joint": {"__id__": 46}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36EOWdgqZHxZV/5XHDTfZ2"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 56}, "target": {"__id__": 26}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7Qq1iy0NA6J+b6Z5NXeRG"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 25}, "_enabled": true, "__prefab": {"__id__": 58}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eVdMUvnVLQamyMr1l3QEp"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f89LrZgUFD9ILDCSJ2G71i", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 61}], "_active": true, "_components": [{"__id__": 77}, {"__id__": 79}, {"__id__": 81}, {"__id__": 83}, {"__id__": 86}, {"__id__": 88}, {"__id__": 90}], "_prefab": {"__id__": 92}, "_lpos": {"__type__": "cc.Vec3", "x": -125.136, "y": -73.741, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 60}, "_children": [{"__id__": 62}, {"__id__": 68}], "_active": true, "_components": [{"__id__": 74}], "_prefab": {"__id__": 76}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 63}, {"__id__": 65}], "_prefab": {"__id__": 67}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 64}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6NdN59uxL0YqzpDrh/pee"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 62}, "_enabled": true, "__prefab": {"__id__": 66}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6aFB58AZDOImUZzwnfPlq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "05WlQ8CfJP0as9i9vYfO7z", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 61}, "_children": [], "_active": true, "_components": [{"__id__": 69}, {"__id__": 71}], "_prefab": {"__id__": 73}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 70}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1ePpd5/U9EvZgBvBpU8gPs"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 68}, "_enabled": true, "__prefab": {"__id__": 72}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fdpmikrstBCbbEenlKuQ5v"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4fxaikRdZHOoCCEvP8dpEh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 61}, "_enabled": true, "__prefab": {"__id__": 75}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a81aEY91ZB1ZgofH19u/gL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "27FfkfGYtEGpN6Xyh/QqbX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 78}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5fc/LzA3ZDZrAQlsa1iZ5S"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 80}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "71u5whABxOEJBq6Gw7nx7w"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 82}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -125.136, "y": -73.741}, "collideConnected": false, "connectedBody": {"__id__": 48}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a7To6dLHtFjZq0mgRIJeXy"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 84}, "clickEvents": [{"__id__": 85}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcsm33Wu9JpZeJwxCai3JY"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 60}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 87}, "bolt": {"__id__": 71}, "thread": {"__id__": 65}, "joint": {"__id__": 81}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "38IWZoaqVPmKC5R7vMZmTK"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 89}, "target": {"__id__": 61}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecK2zJdG5DZKlWpdQujs+W"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 60}, "_enabled": true, "__prefab": {"__id__": 91}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3e9+U/SGtCLrq5FtfGC+uL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "86tX8Oqk9BipUkQfqpGABx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 94}], "_active": true, "_components": [{"__id__": 110}, {"__id__": 112}, {"__id__": 114}, {"__id__": 116}, {"__id__": 119}, {"__id__": 121}, {"__id__": 123}], "_prefab": {"__id__": 125}, "_lpos": {"__type__": "cc.Vec3", "x": 123.46, "y": 66.479, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 93}, "_children": [{"__id__": 95}, {"__id__": 101}], "_active": true, "_components": [{"__id__": 107}], "_prefab": {"__id__": 109}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 96}, {"__id__": 98}], "_prefab": {"__id__": 100}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 97}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6NtPpwNlJy6Nb017fY0JI"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 95}, "_enabled": true, "__prefab": {"__id__": 99}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11RNLXY9pBJ4jY62rYLnVH"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "f2bnbBxaVPcbhsUJ0hMeYF", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 94}, "_children": [], "_active": true, "_components": [{"__id__": 102}, {"__id__": 104}], "_prefab": {"__id__": 106}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 103}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a3dl38hepDq44rOxjlmNC8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 101}, "_enabled": true, "__prefab": {"__id__": 105}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "23P+nuFxpDMad5221hZ7RA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "31MR3TPypEFJvUMBQyRbTb", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 94}, "_enabled": true, "__prefab": {"__id__": 108}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "93ESkhaKNE+KZ4h16ryQYU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "119NEWaM1CQrhdKySlGLw9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 111}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9sj2Wq2ZB04GzAk4pErq2"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 113}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84GzHF5CxM37Fu07CLkri4"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 115}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 123.46, "y": 66.479}, "collideConnected": false, "connectedBody": {"__id__": 48}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f1HhTLG9ZKU45K1nrjRrMp"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 117}, "clickEvents": [{"__id__": 118}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "18OtgF+TJFyqoBHuSRZYrH"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 93}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 120}, "bolt": {"__id__": 104}, "thread": {"__id__": 98}, "joint": {"__id__": 114}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9bArZotD1LrI6o7w0d4B6K"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 122}, "target": {"__id__": 94}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cavbNYIBEpqWUG8P5o1k2"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 93}, "_enabled": true, "__prefab": {"__id__": 124}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eFwd3u01MLoPQNBneHNEA"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "68X2Qzkk1L07Muh4LUaxEh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 2}, "_children": [{"__id__": 127}], "_active": true, "_components": [{"__id__": 143}, {"__id__": 145}, {"__id__": 147}, {"__id__": 149}, {"__id__": 152}, {"__id__": 154}, {"__id__": 156}], "_prefab": {"__id__": 158}, "_lpos": {"__type__": "cc.Vec3", "x": 123.46, "y": -74.3, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 126}, "_children": [{"__id__": 128}, {"__id__": 134}], "_active": true, "_components": [{"__id__": 140}], "_prefab": {"__id__": 142}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 127}, "_children": [], "_active": true, "_components": [{"__id__": 129}, {"__id__": 131}], "_prefab": {"__id__": 133}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 130}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bszMJ1gNJo5oWyUQqWiEA"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 128}, "_enabled": true, "__prefab": {"__id__": 132}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31rweEcOtD55/tZ3907u6T"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "faDFspUhtHt5zIVEJsaNKJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 127}, "_children": [], "_active": true, "_components": [{"__id__": 135}, {"__id__": 137}], "_prefab": {"__id__": 139}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 136}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60hwwgFslEUqntYwOGfz+m"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 134}, "_enabled": true, "__prefab": {"__id__": 138}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "194k2BIwJHhaQ1WbGCB/Jf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dcL9smFeJLObrFIqIU4Tj1", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 127}, "_enabled": true, "__prefab": {"__id__": 141}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "11IZen/ORK46HN2RIO1imQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "63IVIFbd1EiLYf/OM9kWew", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 144}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9ehSreHDJNkaPkKlRzU1jA"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 146}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aeLrg2+RPrLl7TM6Nu+Sb"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 148}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 123.46, "y": -74.3}, "collideConnected": false, "connectedBody": {"__id__": 48}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "788JnBZAZKWptNmQeFyfQi"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 150}, "clickEvents": [{"__id__": 151}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0esD95PYlOzLAc9CuKj7aE"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 126}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 153}, "bolt": {"__id__": 137}, "thread": {"__id__": 131}, "joint": {"__id__": 147}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e6l7yqTNJIwba2XuYg0xRg"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 155}, "target": {"__id__": 127}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "20y8yGiOhInI+o5HPykKXy"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 126}, "_enabled": true, "__prefab": {"__id__": 157}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39rBXMNDJJqbxasjVBgVP7"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "484OlzAlBGt7a+P2qeKkMC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 160}, "_contentSize": {"__type__": "cc.Size", "width": 408, "height": 286}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "35scH6AO5I6rb/fxCjTIkI"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 162}, "img": {"__id__": 22}, "shadow": {"__id__": 7}, "body": {"__id__": 163}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6d36AKuTFBMqOVdNaghH7X"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 2}, "_enabled": true, "__prefab": {"__id__": 164}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -142, "y": 143}, {"__type__": "cc.Vec2", "x": -165, "y": 136}, {"__type__": "cc.Vec2", "x": -180, "y": 126}, {"__type__": "cc.Vec2", "x": -195, "y": 108}, {"__type__": "cc.Vec2", "x": -204, "y": 81}, {"__type__": "cc.Vec2", "x": -204, "y": -81}, {"__type__": "cc.Vec2", "x": -193, "y": -111}, {"__type__": "cc.Vec2", "x": -169, "y": -134}, {"__type__": "cc.Vec2", "x": -136, "y": -143}, {"__type__": "cc.Vec2", "x": 145, "y": -142}, {"__type__": "cc.Vec2", "x": 169, "y": -134}, {"__type__": "cc.Vec2", "x": 187, "y": -119}, {"__type__": "cc.Vec2", "x": 193, "y": -111}, {"__type__": "cc.Vec2", "x": 191, "y": -108}, {"__type__": "cc.Vec2", "x": 195, "y": -108}, {"__type__": "cc.Vec2", "x": 199, "y": -99}, {"__type__": "cc.Vec2", "x": 204, "y": -76}, {"__type__": "cc.Vec2", "x": 203, "y": 85}, {"__type__": "cc.Vec2", "x": 194, "y": 110}, {"__type__": "cc.Vec2", "x": 172, "y": 132}, {"__type__": "cc.Vec2", "x": 142, "y": 143}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bcz+MKehKxLM099YdGnLK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "40OYifmH9Kd6/Cz4+Zp4aT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "128", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 167}, {"__id__": 183}, {"__id__": 189}], "_active": true, "_components": [{"__id__": 224}, {"__id__": 212}, {"__id__": 226}, {"__id__": 228}], "_prefab": {"__id__": 230}, "_lpos": {"__type__": "cc.Vec3", "x": 97, "y": 3817.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 166}, "_children": [{"__id__": 168}], "_active": true, "_components": [{"__id__": 174}, {"__id__": 176}, {"__id__": 178}, {"__id__": 180}], "_prefab": {"__id__": 182}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 167}, "_children": [], "_active": true, "_components": [{"__id__": 169}, {"__id__": 171}], "_prefab": {"__id__": 173}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 170}, "_contentSize": {"__type__": "cc.Size", "width": 294, "height": 207}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "56n+6waLNH4J/QJuO+K8/U"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 168}, "_enabled": true, "__prefab": {"__id__": 172}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "6479ae3a-d55a-42db-a693-82b283a33ca5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b6rnUKaA5EZqePX3mgTIMM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dF5Y9OMdFE5qGHJ9ibKo2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 175}, "_contentSize": {"__type__": "cc.Size", "width": 294, "height": 207}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7aYPp5ehlJ5rk/CGpRVSNj"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 177}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "92W/IrR9FF/pGtSL8huMCM"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 179}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "6479ae3a-d55a-42db-a693-82b283a33ca5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "51Y2tmnglHmY68ShpuObPg"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 167}, "_enabled": true, "__prefab": {"__id__": 181}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "898ui3jIxFwp3b3zt9Mly2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "00qv31MHVKZ7JeJuXqtvBh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 166}, "_children": [], "_active": true, "_components": [{"__id__": 184}, {"__id__": 186}], "_prefab": {"__id__": 188}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 185}, "_contentSize": {"__type__": "cc.Size", "width": 294, "height": 207}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cY5HgR9hIYKyOfewtHxXo"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 183}, "_enabled": true, "__prefab": {"__id__": 187}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "6479ae3a-d55a-42db-a693-82b283a33ca5@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15vqN3xZdG5amUxQzRi+k+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fbJQewVWVCgrSFkq8cvGih", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 166}, "_children": [{"__id__": 190}], "_active": true, "_components": [{"__id__": 206}, {"__id__": 208}, {"__id__": 210}, {"__id__": 214}, {"__id__": 217}, {"__id__": 219}, {"__id__": 221}], "_prefab": {"__id__": 223}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 189}, "_children": [{"__id__": 191}, {"__id__": 197}], "_active": true, "_components": [{"__id__": 203}], "_prefab": {"__id__": 205}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 192}, {"__id__": 194}], "_prefab": {"__id__": 196}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 193}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bcBid5D5FJAphOe5Xl7R8X"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 191}, "_enabled": true, "__prefab": {"__id__": 195}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0190InyfhN5boxO9kl3K0c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64v8KKw81GcpoC/vx/tWbO", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 190}, "_children": [], "_active": true, "_components": [{"__id__": 198}, {"__id__": 200}], "_prefab": {"__id__": 202}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 199}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63rMVvo0JNHbKHA0gIyga/"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 197}, "_enabled": true, "__prefab": {"__id__": 201}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0kpjIp/9MrqDH/b9T8fT5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bfdp2SKsFJ2LNCMSDmYvPu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 190}, "_enabled": true, "__prefab": {"__id__": 204}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ffXG2Y7WNIJp8UqEvlCXQQ"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1e8DRe9eRBOqjis9633Cv5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 207}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1akLDjN4pOnrAvY4gf++yv"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 209}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4ce5etHKlAVJIiVfIRnIEn"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 211}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 212}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f761qSBBBIe5pvf/Y1DEkn"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 213}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d0kwqEE3FJOK368IPic9O1"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 215}, "clickEvents": [{"__id__": 216}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8m8PbW/VG5aW3EK2K0ksK"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 189}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 218}, "bolt": {"__id__": 200}, "thread": {"__id__": 194}, "joint": {"__id__": 210}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74iQBGm59D/5dSXwwg8VoH"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 220}, "target": {"__id__": 190}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "91JOj5LN5N9ZmLgQ4MheBO"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 189}, "_enabled": true, "__prefab": {"__id__": 222}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "671G1G8BdNMYDCCu+gwP7+"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64S48mlD1I871wB2oK0IiW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 225}, "_contentSize": {"__type__": "cc.Size", "width": 294, "height": 207}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a4gp80WdJM5ZPodxmeke6T"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 227}, "img": {"__id__": 186}, "shadow": {"__id__": 171}, "body": {"__id__": 228}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "671xprevBANpQJfOFWDsvW"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 166}, "_enabled": true, "__prefab": {"__id__": 229}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": 14, "y": 103.5}, {"__type__": "cc.Vec2", "x": -16, "y": 95.5}, {"__type__": "cc.Vec2", "x": -40, "y": 77.5}, {"__type__": "cc.Vec2", "x": -61, "y": 79.5}, {"__type__": "cc.Vec2", "x": -84, "y": 71.5}, {"__type__": "cc.Vec2", "x": -94, "y": 63.5}, {"__type__": "cc.Vec2", "x": -101, "y": 52.5}, {"__type__": "cc.Vec2", "x": -106, "y": 31.5}, {"__type__": "cc.Vec2", "x": -121, "y": 23.5}, {"__type__": "cc.Vec2", "x": -136, "y": 7.5}, {"__type__": "cc.Vec2", "x": -147, "y": -22.5}, {"__type__": "cc.Vec2", "x": -145, "y": -51.5}, {"__type__": "cc.Vec2", "x": -137, "y": -70.5}, {"__type__": "cc.Vec2", "x": -118, "y": -90.5}, {"__type__": "cc.Vec2", "x": -97, "y": -100.5}, {"__type__": "cc.Vec2", "x": -79, "y": -103.5}, {"__type__": "cc.Vec2", "x": 78, "y": -103.5}, {"__type__": "cc.Vec2", "x": 103, "y": -98.5}, {"__type__": "cc.Vec2", "x": 123, "y": -86.5}, {"__type__": "cc.Vec2", "x": 137, "y": -70.5}, {"__type__": "cc.Vec2", "x": 145, "y": -52.5}, {"__type__": "cc.Vec2", "x": 147, "y": -27.5}, {"__type__": "cc.Vec2", "x": 138, "y": 4.5}, {"__type__": "cc.Vec2", "x": 121, "y": 23.5}, {"__type__": "cc.Vec2", "x": 101, "y": 33.5}, {"__type__": "cc.Vec2", "x": 91, "y": 62.5}, {"__type__": "cc.Vec2", "x": 77, "y": 80.5}, {"__type__": "cc.Vec2", "x": 49, "y": 97.5}, {"__type__": "cc.Vec2", "x": 26, "y": 103.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecee43yGlHeZP3gEJzU9Pf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ccHAngn2FJbqWrfmTWo7QT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "399", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 232}, {"__id__": 248}, {"__id__": 254}], "_active": true, "_components": [{"__id__": 289}, {"__id__": 277}, {"__id__": 291}, {"__id__": 293}], "_prefab": {"__id__": 295}, "_lpos": {"__type__": "cc.Vec3", "x": 90.5, "y": 1553.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [{"__id__": 233}], "_active": true, "_components": [{"__id__": 239}, {"__id__": 241}, {"__id__": 243}, {"__id__": 245}], "_prefab": {"__id__": 247}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 232}, "_children": [], "_active": true, "_components": [{"__id__": 234}, {"__id__": 236}], "_prefab": {"__id__": 238}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 235}, "_contentSize": {"__type__": "cc.Size", "width": 281, "height": 367}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a84obVD+1LWqI9LXih0lAr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 233}, "_enabled": true, "__prefab": {"__id__": 237}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "4d0d6505-c91d-45fd-abab-a1b26b049fa8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "acoiI+9kJHPY5et8V0r+VV"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "dfo7rhLnVP07YndFqEetQ8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 240}, "_contentSize": {"__type__": "cc.Size", "width": 281, "height": 367}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecAkYiat5PO5jamjpPdvun"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 242}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7cSBuaAPtA4K9H+BL9LVZD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 244}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "4d0d6505-c91d-45fd-abab-a1b26b049fa8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c65+emmFlHbpG3fy/5YOOF"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 232}, "_enabled": true, "__prefab": {"__id__": 246}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bDcpvUtdFe5E9LjLSYXlU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "82sxjrY3VK5Y60ht91GmQ5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [], "_active": true, "_components": [{"__id__": 249}, {"__id__": 251}], "_prefab": {"__id__": 253}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 250}, "_contentSize": {"__type__": "cc.Size", "width": 281, "height": 367}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62mGEAWhlM9YNzqEGavbjd"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 248}, "_enabled": true, "__prefab": {"__id__": 252}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "4d0d6505-c91d-45fd-abab-a1b26b049fa8@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9aWATTRMNErJIWx0kq4Vra"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "88sE0MQV9DnL6aD94MmK9x", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 231}, "_children": [{"__id__": 255}], "_active": true, "_components": [{"__id__": 271}, {"__id__": 273}, {"__id__": 275}, {"__id__": 279}, {"__id__": 282}, {"__id__": 284}, {"__id__": 286}], "_prefab": {"__id__": 288}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 254}, "_children": [{"__id__": 256}, {"__id__": 262}], "_active": true, "_components": [{"__id__": 268}], "_prefab": {"__id__": 270}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 257}, {"__id__": 259}], "_prefab": {"__id__": 261}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 258}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6wnffqUBLKolEXfykswdw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 256}, "_enabled": true, "__prefab": {"__id__": 260}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cc3UkDQU9PTphnkQYaALOb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "41JJ42QstJhqC8pAHLlqoY", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 255}, "_children": [], "_active": true, "_components": [{"__id__": 263}, {"__id__": 265}], "_prefab": {"__id__": 267}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 264}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7deafMRZZNnLErGQhwgQMc"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 262}, "_enabled": true, "__prefab": {"__id__": 266}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "cd9FczHJhN8oAwwzO7/kzr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99IVP4GflOMq3S5keMCi6E", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 255}, "_enabled": true, "__prefab": {"__id__": 269}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "05Aaps4XZK95l4syP4dc1g"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e1DN6Ya+FHSokSiZWNr+AE", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 272}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27DyibkGNDOrJ03XBZY3Hz"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 274}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75enJdJnlIjpeCaUQVXh85"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 276}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 277}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ac6BDZcJFHZYWRceC0rk1e"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 278}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dcesmdgv9CsJuYx93aOAkB"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 280}, "clickEvents": [{"__id__": 281}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e0rnjb07pPb5PXyen2hsd3"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 254}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 283}, "bolt": {"__id__": 265}, "thread": {"__id__": 259}, "joint": {"__id__": 275}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "964xTXwDJCxbDFM185tgZh"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 285}, "target": {"__id__": 255}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "63IBTsBMpOQrk+aFcvGl/S"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 254}, "_enabled": true, "__prefab": {"__id__": 287}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cpE7xAD5Kzq5uMUeCqRwr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b9fjzy00RKpKEx0F4RFxLC", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 290}, "_contentSize": {"__type__": "cc.Size", "width": 281, "height": 367}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d51MyZX2BK/6dk+djtF0Gw"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 292}, "img": {"__id__": 251}, "shadow": {"__id__": 236}, "body": {"__id__": 293}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bdkVLXy99PC6wA66bO344Z"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 231}, "_enabled": true, "__prefab": {"__id__": 294}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -8.5, "y": 183.5}, {"__type__": "cc.Vec2", "x": -18.5, "y": 179.5}, {"__type__": "cc.Vec2", "x": -19.5, "y": 175.5}, {"__type__": "cc.Vec2", "x": -22.5, "y": 176.5}, {"__type__": "cc.Vec2", "x": -31.5, "y": 165.5}, {"__type__": "cc.Vec2", "x": -47.5, "y": 136.5}, {"__type__": "cc.Vec2", "x": -46.5, "y": 132.5}, {"__type__": "cc.Vec2", "x": -48.5, "y": 133.5}, {"__type__": "cc.Vec2", "x": -59.5, "y": 115.5}, {"__type__": "cc.Vec2", "x": -140.5, "y": -32.5}, {"__type__": "cc.Vec2", "x": -136.5, "y": -50.5}, {"__type__": "cc.Vec2", "x": -128.5, "y": -55.5}, {"__type__": "cc.Vec2", "x": -129.5, "y": -58.5}, {"__type__": "cc.Vec2", "x": -120.5, "y": -62.5}, {"__type__": "cc.Vec2", "x": -23.5, "y": -64.5}, {"__type__": "cc.Vec2", "x": -23.5, "y": -156.5}, {"__type__": "cc.Vec2", "x": -16.5, "y": -174.5}, {"__type__": "cc.Vec2", "x": 3.5, "y": -183.5}, {"__type__": "cc.Vec2", "x": 19.5, "y": -179.5}, {"__type__": "cc.Vec2", "x": 28.5, "y": -170.5}, {"__type__": "cc.Vec2", "x": 39.5, "y": -151.5}, {"__type__": "cc.Vec2", "x": 38.5, "y": -147.5}, {"__type__": "cc.Vec2", "x": 42.5, "y": -146.5}, {"__type__": "cc.Vec2", "x": 48.5, "y": -136.5}, {"__type__": "cc.Vec2", "x": 98.5, "y": -47.5}, {"__type__": "cc.Vec2", "x": 100.5, "y": -38.5}, {"__type__": "cc.Vec2", "x": 103.5, "y": -38.5}, {"__type__": "cc.Vec2", "x": 136.5, "y": 19.5}, {"__type__": "cc.Vec2", "x": 140.5, "y": 33.5}, {"__type__": "cc.Vec2", "x": 138.5, "y": 46.5}, {"__type__": "cc.Vec2", "x": 133.5, "y": 55.5}, {"__type__": "cc.Vec2", "x": 122.5, "y": 62.5}, {"__type__": "cc.Vec2", "x": 23.5, "y": 65.5}, {"__type__": "cc.Vec2", "x": 22.5, "y": 165.5}, {"__type__": "cc.Vec2", "x": 8.5, "y": 180.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00tigRE/dMn7nDAHCSYhR8"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3bOONy1SVEUakJz3KLyfk9", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "134", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 297}, {"__id__": 313}, {"__id__": 319}], "_active": true, "_components": [{"__id__": 354}, {"__id__": 342}, {"__id__": 356}, {"__id__": 358}], "_prefab": {"__id__": 360}, "_lpos": {"__type__": "cc.Vec3", "x": 89, "y": -861.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 296}, "_children": [{"__id__": 298}], "_active": true, "_components": [{"__id__": 304}, {"__id__": 306}, {"__id__": 308}, {"__id__": 310}], "_prefab": {"__id__": 312}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 297}, "_children": [], "_active": true, "_components": [{"__id__": 299}, {"__id__": 301}], "_prefab": {"__id__": 303}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 300}, "_contentSize": {"__type__": "cc.Size", "width": 278, "height": 267}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ck+2sXilLk5Gc+qSbw6sH"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 298}, "_enabled": true, "__prefab": {"__id__": 302}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "f863578d-d53b-4ed3-9338-d6964e07c251@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deRIwrczVBraBDLkTJoK7k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3e5FG9bytDspobObRgFoS5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 305}, "_contentSize": {"__type__": "cc.Size", "width": 278, "height": 267}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4d1UtQlKtADpUD2E9O22qN"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 307}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7u6O+eWBJb74sgl2wgLwu"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 309}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "f863578d-d53b-4ed3-9338-d6964e07c251@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2cHUgWqSpO9qfbBUDqQTnY"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 297}, "_enabled": true, "__prefab": {"__id__": 311}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0eAB+ACTBPsZDQSMk7vqSD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7eq7a96vJHPq596D9+a+ws", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 296}, "_children": [], "_active": true, "_components": [{"__id__": 314}, {"__id__": 316}], "_prefab": {"__id__": 318}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": {"__id__": 315}, "_contentSize": {"__type__": "cc.Size", "width": 278, "height": 267}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "09GX0+LYxOtY5zENx84nVy"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 313}, "_enabled": true, "__prefab": {"__id__": 317}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "f863578d-d53b-4ed3-9338-d6964e07c251@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27/773R59GdbteZlm+Gze3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0jtdjI0FPNoXeuOllEqth", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 296}, "_children": [{"__id__": 320}], "_active": true, "_components": [{"__id__": 336}, {"__id__": 338}, {"__id__": 340}, {"__id__": 344}, {"__id__": 347}, {"__id__": 349}, {"__id__": 351}], "_prefab": {"__id__": 353}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 319}, "_children": [{"__id__": 321}, {"__id__": 327}], "_active": true, "_components": [{"__id__": 333}], "_prefab": {"__id__": 335}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 320}, "_children": [], "_active": true, "_components": [{"__id__": 322}, {"__id__": 324}], "_prefab": {"__id__": 326}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": {"__id__": 323}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2ae/+dIApDoLeLJ+VP2S/8"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 321}, "_enabled": true, "__prefab": {"__id__": 325}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2bNmKuizpN760O34DAJS3z"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "50ZP7ff9FBhLdjY31Ld/SP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 320}, "_children": [], "_active": true, "_components": [{"__id__": 328}, {"__id__": 330}], "_prefab": {"__id__": 332}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 329}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0c0oExh7dEVIxP3+a0iRx7"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 327}, "_enabled": true, "__prefab": {"__id__": 331}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "66u2a1xNVEkZ9jIjEG/c0v"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "daTYZQfmZEVqXim9nV4WYn", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 320}, "_enabled": true, "__prefab": {"__id__": 334}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17LX2/n0dG/IVZeqmc2nUM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc308CVGpIX4U9r2WzDh3q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 337}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f6/HqDMfpHIac35XOaZh7V"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 339}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "257OnJbYdBd7aQJrpk8Rrv"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 341}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 342}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5eh/wCcltJHrBVLGTx6xEG"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 343}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e9/YoLappOQLSvUHE64Buh"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 345}, "clickEvents": [{"__id__": 346}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "31wzZ051JOf4tPF9PNJSe0"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 319}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 348}, "bolt": {"__id__": 330}, "thread": {"__id__": 324}, "joint": {"__id__": 340}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "52rgYvjJ5L3oVQVABO7whE"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 350}, "target": {"__id__": 320}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5UOfBb8VGs6iJ+cbrpyLw"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 319}, "_enabled": true, "__prefab": {"__id__": 352}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15WPV/t8FI6LHNeT9UFdAK"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "34DqhJ8YRPLYTkccYimHRj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 355}, "_contentSize": {"__type__": "cc.Size", "width": 278, "height": 267}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "95pU4FXDtMA63GzJlmqJOt"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 357}, "img": {"__id__": 316}, "shadow": {"__id__": 301}, "body": {"__id__": 358}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17ohz1dOpHGbtzLW3F9HjF"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 296}, "_enabled": true, "__prefab": {"__id__": 359}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -4, "y": 133.5}, {"__type__": "cc.Vec2", "x": -25, "y": 123.5}, {"__type__": "cc.Vec2", "x": -57, "y": 68.5}, {"__type__": "cc.Vec2", "x": -119, "y": 54.5}, {"__type__": "cc.Vec2", "x": -138, "y": 32.5}, {"__type__": "cc.Vec2", "x": -136, "y": 7.5}, {"__type__": "cc.Vec2", "x": -93, "y": -42.5}, {"__type__": "cc.Vec2", "x": -99, "y": -102.5}, {"__type__": "cc.Vec2", "x": -86, "y": -127.5}, {"__type__": "cc.Vec2", "x": -58, "y": -133.5}, {"__type__": "cc.Vec2", "x": 5, "y": -109.5}, {"__type__": "cc.Vec2", "x": 53, "y": -132.5}, {"__type__": "cc.Vec2", "x": 83, "y": -129.5}, {"__type__": "cc.Vec2", "x": 99, "y": -103.5}, {"__type__": "cc.Vec2", "x": 93, "y": -42.5}, {"__type__": "cc.Vec2", "x": 131, "y": 0.5}, {"__type__": "cc.Vec2", "x": 139, "y": 18.5}, {"__type__": "cc.Vec2", "x": 137, "y": 36.5}, {"__type__": "cc.Vec2", "x": 124, "y": 52.5}, {"__type__": "cc.Vec2", "x": 56, "y": 69.5}, {"__type__": "cc.Vec2", "x": 25, "y": 123.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "00qAzPf4BEKYl8l24BEPfT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ceMVthtJZLxKe1I5HnXHyQ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "254", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 362}, {"__id__": 378}, {"__id__": 384}], "_active": true, "_components": [{"__id__": 419}, {"__id__": 407}, {"__id__": 421}, {"__id__": 423}], "_prefab": {"__id__": 425}, "_lpos": {"__type__": "cc.Vec3", "x": 85, "y": -1112.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 361}, "_children": [{"__id__": 363}], "_active": true, "_components": [{"__id__": 369}, {"__id__": 371}, {"__id__": 373}, {"__id__": 375}], "_prefab": {"__id__": 377}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 362}, "_children": [], "_active": true, "_components": [{"__id__": 364}, {"__id__": 366}], "_prefab": {"__id__": 368}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 365}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 235}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d3Knh67gBF3r5nKYMhhcCP"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 363}, "_enabled": true, "__prefab": {"__id__": 367}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "e58ea972-0214-4b0a-bf55-5f3df824e9f9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6f178pXclEqLV4108kczDM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bc8G7+5ORLvK4hi8Sfs82c", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 370}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 235}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fb6rpomwlG47q/Sr6eLCKp"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 372}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1eb1oxhwlOcaSszDzLCUXx"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 374}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "e58ea972-0214-4b0a-bf55-5f3df824e9f9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3fN5tUX5HKIILbiQaipx/"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 362}, "_enabled": true, "__prefab": {"__id__": 376}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7afUHb7wpKMLGpSE0SBTl6"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ae1ArT/UNAI4kH72WLWBez", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 361}, "_children": [], "_active": true, "_components": [{"__id__": 379}, {"__id__": 381}], "_prefab": {"__id__": 383}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 378}, "_enabled": true, "__prefab": {"__id__": 380}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 235}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "84xFElsCdIIant33ZBlL/D"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 378}, "_enabled": true, "__prefab": {"__id__": 382}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "e58ea972-0214-4b0a-bf55-5f3df824e9f9@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3bX+iQ0RBM84Wkxzc1gU5c"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4b7Mcx3ydPWYmBRmepNTw8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 361}, "_children": [{"__id__": 385}], "_active": true, "_components": [{"__id__": 401}, {"__id__": 403}, {"__id__": 405}, {"__id__": 409}, {"__id__": 412}, {"__id__": 414}, {"__id__": 416}], "_prefab": {"__id__": 418}, "_lpos": {"__type__": "cc.Vec3", "x": -2, "y": 6, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 384}, "_children": [{"__id__": 386}, {"__id__": 392}], "_active": true, "_components": [{"__id__": 398}], "_prefab": {"__id__": 400}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 385}, "_children": [], "_active": true, "_components": [{"__id__": 387}, {"__id__": 389}], "_prefab": {"__id__": 391}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 386}, "_enabled": true, "__prefab": {"__id__": 388}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fBCCSmSlM8I0ojgkSnmX6"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 386}, "_enabled": true, "__prefab": {"__id__": 390}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c2fYWmb1NBH6KYCHpBxSg/"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e0LuOF765BPYq6cs8CaDS0", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 385}, "_children": [], "_active": true, "_components": [{"__id__": 393}, {"__id__": 395}], "_prefab": {"__id__": 397}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 392}, "_enabled": true, "__prefab": {"__id__": 394}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a43cXjBFZBcr/PVTLaK/uL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 392}, "_enabled": true, "__prefab": {"__id__": 396}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d61eEXDKNK0os8b8dBbFdL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cJsmYNqpM74JWElmcOxjG", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 385}, "_enabled": true, "__prefab": {"__id__": 399}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "caJTUOvs1FMYCVK0NOFLSM"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "99qgCHd4tKgJ5Y8qgOiGBg", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 402}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d9OE4Zw5VNMaitypofDg9I"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 404}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "abRPuFVTFEALvmrb7ve7nV"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 406}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -2, "y": 6}, "collideConnected": false, "connectedBody": {"__id__": 407}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "169SprO65JlZcg8+qqy4o+"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 408}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4mXZ8AlBO2aw6r1v+PJkb"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 410}, "clickEvents": [{"__id__": 411}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "15fwb6sRRNZo9IYtLnrY+c"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 384}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 413}, "bolt": {"__id__": 395}, "thread": {"__id__": 389}, "joint": {"__id__": 405}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96L/PJTCZHLINZfkn5XyaZ"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 415}, "target": {"__id__": 385}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "33374VUyFFsJsALvo+DV6u"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 384}, "_enabled": true, "__prefab": {"__id__": 417}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8PAMEa2dIcL3/L+gU2Wpg"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1exPmS9ZBCC5LJQaKoMezB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 420}, "_contentSize": {"__type__": "cc.Size", "width": 270, "height": 235}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "39WN4GUgtM5aP+/sVaiuMZ"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 422}, "img": {"__id__": 381}, "shadow": {"__id__": 366}, "body": {"__id__": 423}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "17TT5bm1FP5abdXGL7qVH+"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 361}, "_enabled": true, "__prefab": {"__id__": 424}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -70, "y": 117.5}, {"__type__": "cc.Vec2", "x": -108, "y": 99.5}, {"__type__": "cc.Vec2", "x": -127, "y": 74.5}, {"__type__": "cc.Vec2", "x": -135, "y": 47.5}, {"__type__": "cc.Vec2", "x": -131, "y": 10.5}, {"__type__": "cc.Vec2", "x": -99, "y": -46.5}, {"__type__": "cc.Vec2", "x": -53, "y": -87.5}, {"__type__": "cc.Vec2", "x": -6, "y": -116.5}, {"__type__": "cc.Vec2", "x": 8, "y": -115.5}, {"__type__": "cc.Vec2", "x": 34, "y": -100.5}, {"__type__": "cc.Vec2", "x": 93, "y": -52.5}, {"__type__": "cc.Vec2", "x": 124, "y": -9.5}, {"__type__": "cc.Vec2", "x": 135, "y": 34.5}, {"__type__": "cc.Vec2", "x": 125, "y": 78.5}, {"__type__": "cc.Vec2", "x": 107, "y": 100.5}, {"__type__": "cc.Vec2", "x": 84, "y": 113.5}, {"__type__": "cc.Vec2", "x": 43, "y": 116.5}, {"__type__": "cc.Vec2", "x": 19, "y": 107.5}, {"__type__": "cc.Vec2", "x": -1, "y": 91.5}, {"__type__": "cc.Vec2", "x": -29, "y": 112.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcOGxMBD5HP4ol7WVv2vFT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "486mOwxsJFDbsUwZuoY1P5", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "730", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 427}, {"__id__": 443}, {"__id__": 449}, {"__id__": 484}, {"__id__": 517}, {"__id__": 550}, {"__id__": 583}, {"__id__": 616}], "_active": true, "_components": [{"__id__": 649}, {"__id__": 472}, {"__id__": 651}, {"__id__": 653}], "_prefab": {"__id__": 655}, "_lpos": {"__type__": "cc.Vec3", "x": 238, "y": -3468, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 428}], "_active": true, "_components": [{"__id__": 434}, {"__id__": 436}, {"__id__": 438}, {"__id__": 440}], "_prefab": {"__id__": 442}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 427}, "_children": [], "_active": true, "_components": [{"__id__": 429}, {"__id__": 431}], "_prefab": {"__id__": 433}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 430}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 526}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9LDf8CxBHzKkaP2ZK0Wd3"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 428}, "_enabled": true, "__prefab": {"__id__": 432}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "429d057a-b43c-41de-807d-30117da5111a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8bjahQs5FAnrKv0mpRGBiS"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8e+mkmuHdMJ7Vv811VJD3q", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 435}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 526}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c9KTYkigRB9r9BflfQ8rJe"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 437}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5bOrCt93xN07TLhj2InZjL"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 439}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "429d057a-b43c-41de-807d-30117da5111a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a38faOFQ9IOJG0TzTKJmIt"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 427}, "_enabled": true, "__prefab": {"__id__": 441}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3vIckFbJJq5+kcqyh65oa"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10eNqjgnNLvpeWEfN8L42H", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [], "_active": true, "_components": [{"__id__": 444}, {"__id__": 446}], "_prefab": {"__id__": 448}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 445}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 526}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fckglvjN1LK4P2n854s12L"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 443}, "_enabled": true, "__prefab": {"__id__": 447}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "429d057a-b43c-41de-807d-30117da5111a@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82tOjtc0hDUpM2ngwcU0dD"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4eqcdUu+ZIIZrfQ9duCYy2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 450}], "_active": true, "_components": [{"__id__": 466}, {"__id__": 468}, {"__id__": 470}, {"__id__": 474}, {"__id__": 477}, {"__id__": 479}, {"__id__": 481}], "_prefab": {"__id__": 483}, "_lpos": {"__type__": "cc.Vec3", "x": -158, "y": -190, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 449}, "_children": [{"__id__": 451}, {"__id__": 457}], "_active": true, "_components": [{"__id__": 463}], "_prefab": {"__id__": 465}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 450}, "_children": [], "_active": true, "_components": [{"__id__": 452}, {"__id__": 454}], "_prefab": {"__id__": 456}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 453}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "41vCo8iCBPxoZZed4rfAnQ"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 451}, "_enabled": true, "__prefab": {"__id__": 455}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9clzmu9p1N1qLoFvJkHrvL"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "1f/QDrw+NG8JvGjVSaN7DB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 450}, "_children": [], "_active": true, "_components": [{"__id__": 458}, {"__id__": 460}], "_prefab": {"__id__": 462}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 459}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eRCo/l2tKnaBoW00b0T2W"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 457}, "_enabled": true, "__prefab": {"__id__": 461}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59+7xmBIdHlpdbYKX0UXOe"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "60Tm7hMBRKUqyq/FuCA/X/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 450}, "_enabled": true, "__prefab": {"__id__": 464}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1bSimzE4pOsrvJ5Y6TdVQc"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "33DEhueCVAf6qQ6jjGeQkP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 467}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "68ImTE6hFDi548Owq1r7XS"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 469}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fop6v0uVE5pIBmP/Vo+RR"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 471}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -158, "y": -190}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2dAeM17TZARKfj0/59NOd3"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 473}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2UZa0lyFIqY6PVvx/Vnib"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 475}, "clickEvents": [{"__id__": 476}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efrRVttX9PK7PNnk3I/umv"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 449}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 478}, "bolt": {"__id__": 460}, "thread": {"__id__": 454}, "joint": {"__id__": 470}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "86rUXZMDhJ+7Sohf+hB38X"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 480}, "target": {"__id__": 450}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d5KUcfMsRJlKWlI2k8PYLk"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 449}, "_enabled": true, "__prefab": {"__id__": 482}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2nJyHbnZPUpszWQ7pgJ6k"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "080GJdUjZDgKVTB7H8aVkN", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 485}], "_active": true, "_components": [{"__id__": 501}, {"__id__": 503}, {"__id__": 505}, {"__id__": 507}, {"__id__": 510}, {"__id__": 512}, {"__id__": 514}], "_prefab": {"__id__": 516}, "_lpos": {"__type__": "cc.Vec3", "x": 160, "y": -190, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 484}, "_children": [{"__id__": 486}, {"__id__": 492}], "_active": true, "_components": [{"__id__": 498}], "_prefab": {"__id__": 500}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 485}, "_children": [], "_active": true, "_components": [{"__id__": 487}, {"__id__": 489}], "_prefab": {"__id__": 491}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 488}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47rKtJIrRMQYR+KQ047miX"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 486}, "_enabled": true, "__prefab": {"__id__": 490}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d6TDiyt45LcoRBYx3g4MHo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "301WhS6QhPxq1HJi6mmKTS", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 485}, "_children": [], "_active": true, "_components": [{"__id__": 493}, {"__id__": 495}], "_prefab": {"__id__": 497}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 492}, "_enabled": true, "__prefab": {"__id__": 494}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fcUpHY9uBP5YsetsJjillV"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 492}, "_enabled": true, "__prefab": {"__id__": 496}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbBzH2eg9AboiJB+pAuu5l"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "24RLFNWMBNwb4NFEAgXjLi", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 485}, "_enabled": true, "__prefab": {"__id__": 499}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "22Yve1dOBFDKg6NlWkk3IT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "9eu9yCudtNS5RMcqOS3EsX", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 502}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6fDQt/4KtHM45vBRxrpwIR"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 504}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e2FT7hK6dHq7NLwLzKXIWV"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 506}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 160, "y": -190}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74+6o81mdBkqHt1lNi7E7z"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 508}, "clickEvents": [{"__id__": 509}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7vUo/F91ElqXU0ysPN5Tw"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 484}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 511}, "bolt": {"__id__": 495}, "thread": {"__id__": 489}, "joint": {"__id__": 505}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8cLLsXyR1O2aXPgI6/Xl3B"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 513}, "target": {"__id__": 485}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "155tOOiHpBD73/nfUGQAOZ"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 484}, "_enabled": true, "__prefab": {"__id__": 515}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eb2fp5yvpNjKdO9Ob/py5t"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "beJ9gnol5DYZxta/qlqivK", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 518}], "_active": true, "_components": [{"__id__": 534}, {"__id__": 536}, {"__id__": 538}, {"__id__": 540}, {"__id__": 543}, {"__id__": 545}, {"__id__": 547}], "_prefab": {"__id__": 549}, "_lpos": {"__type__": "cc.Vec3", "x": 132, "y": 164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 517}, "_children": [{"__id__": 519}, {"__id__": 525}], "_active": true, "_components": [{"__id__": 531}], "_prefab": {"__id__": 533}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 518}, "_children": [], "_active": true, "_components": [{"__id__": 520}, {"__id__": 522}], "_prefab": {"__id__": 524}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 521}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "edz0lA/ExFJZleSB3UjKmG"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 519}, "_enabled": true, "__prefab": {"__id__": 523}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "03aYLmOaZKyp+XCfQRNicl"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "47o97csCBDwIyM62Y8CDaT", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 518}, "_children": [], "_active": true, "_components": [{"__id__": 526}, {"__id__": 528}], "_prefab": {"__id__": 530}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 527}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "44bpThx5dGF7xZl1CKeDsk"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 525}, "_enabled": true, "__prefab": {"__id__": 529}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a8driCYkVPMpCyFIhZH6eE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "43zFVj8MVIbqJCGnJbcsL4", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 518}, "_enabled": true, "__prefab": {"__id__": 532}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ddtyI0SxdEzIQXvteylTTb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "605B6JpjBPf4DPJkysOnCe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 535}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "27r0nXyktIA63v4Uy8g8Qi"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 537}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4c5HnfR9xBVYE4ZhbUCFxB"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 539}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 132, "y": 164}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "59V83krvRBT4AiI6+tsLAK"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 541}, "clickEvents": [{"__id__": 542}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c6bLe905lHuJ1/805ZCf9B"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 517}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 544}, "bolt": {"__id__": 528}, "thread": {"__id__": 522}, "joint": {"__id__": 538}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5aV8IakS1F7KY7U8eN/Y8z"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 546}, "target": {"__id__": 518}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "24JuwzrsZHOLM3/f7q8z5S"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 517}, "_enabled": true, "__prefab": {"__id__": 548}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1cTXAyAtFCU4HzBiZ1VIB3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d3HvhkDbxN9o1hud/x3Tlx", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 551}], "_active": true, "_components": [{"__id__": 567}, {"__id__": 569}, {"__id__": 571}, {"__id__": 573}, {"__id__": 576}, {"__id__": 578}, {"__id__": 580}], "_prefab": {"__id__": 582}, "_lpos": {"__type__": "cc.Vec3", "x": -227, "y": -9, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 550}, "_children": [{"__id__": 552}, {"__id__": 558}], "_active": true, "_components": [{"__id__": 564}], "_prefab": {"__id__": 566}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 551}, "_children": [], "_active": true, "_components": [{"__id__": 553}, {"__id__": 555}], "_prefab": {"__id__": 557}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 554}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96X0AOpatNyLwO7Llu8/1Z"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 552}, "_enabled": true, "__prefab": {"__id__": 556}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4LSWPSBFK/pedmPVdHMR2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6asSthzgpLFpZRUcdbVAUB", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 551}, "_children": [], "_active": true, "_components": [{"__id__": 559}, {"__id__": 561}], "_prefab": {"__id__": 563}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 560}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "80S229JRpDZqz0C2mprPtr"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 558}, "_enabled": true, "__prefab": {"__id__": 562}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3drePN9ChOSKpYgmi1CMHr"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5jeO3RcpIGrfvhjr51V2F", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 551}, "_enabled": true, "__prefab": {"__id__": 565}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d99V+ThtxBkoJG+elyCkWf"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "970Qr0wdhPh4gHYv04ouuu", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 568}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49v3+krltNKq3u8l+lVRdj"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 570}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "65JlrX+O5JQaKsk7g4X5Mv"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 572}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -227, "y": -9}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ecNExF48xBDof2fy+teaNO"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 574}, "clickEvents": [{"__id__": 575}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75OVDUkpxBULC3oGet+hOB"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 550}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 577}, "bolt": {"__id__": 561}, "thread": {"__id__": 555}, "joint": {"__id__": 571}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0cjLqJatpO2LIqVtgoWGWM"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 579}, "target": {"__id__": 551}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "c3MdEKxTJEELf1lnHAVOH4"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 550}, "_enabled": true, "__prefab": {"__id__": 581}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "010HwnRIdDgJm9zvQ2NP4E"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6eE6GyXRlG9biXdZYkyqrd", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 584}], "_active": true, "_components": [{"__id__": 600}, {"__id__": 602}, {"__id__": 604}, {"__id__": 606}, {"__id__": 609}, {"__id__": 611}, {"__id__": 613}], "_prefab": {"__id__": 615}, "_lpos": {"__type__": "cc.Vec3", "x": 229.99999999999997, "y": -9, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 583}, "_children": [{"__id__": 585}, {"__id__": 591}], "_active": true, "_components": [{"__id__": 597}], "_prefab": {"__id__": 599}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 584}, "_children": [], "_active": true, "_components": [{"__id__": 586}, {"__id__": 588}], "_prefab": {"__id__": 590}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 585}, "_enabled": true, "__prefab": {"__id__": 587}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aDLmb+CFARbK7999ycDra"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 585}, "_enabled": true, "__prefab": {"__id__": 589}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "47+VI5l41IuaYjFGGFr0pT"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "7500pJQU9BhrN60aM7ZjDe", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 584}, "_children": [], "_active": true, "_components": [{"__id__": 592}, {"__id__": 594}], "_prefab": {"__id__": 596}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 593}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7B3H74kdHoJXxgKdAlKWU"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 591}, "_enabled": true, "__prefab": {"__id__": 595}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "57SieMHb1LqqhJHhtWpdkb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5dECIljndAxK1rDlXeSdlM", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 584}, "_enabled": true, "__prefab": {"__id__": 598}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3fGzWBmrdH8KVV+52Fm8k2"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "5azfxkUkBKN53YLUe0xWMI", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 601}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "a08oCWwLJK2Z6QK3CI8LXA"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 603}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "6cYONOlP9OCZRKQXniWoiO"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 605}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 229.99999999999997, "y": -9}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7i2XZo6lCi6KG+TUea3Vi"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 607}, "clickEvents": [{"__id__": 608}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0efjAyM9hLWLuca4S3TOEP"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 583}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 610}, "bolt": {"__id__": 594}, "thread": {"__id__": 588}, "joint": {"__id__": 604}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74JqQOHP5PnoVe1UZMO4HP"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 612}, "target": {"__id__": 584}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9nilDe49POKlVgup4+439"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 583}, "_enabled": true, "__prefab": {"__id__": 614}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b3YLOA7ORGDZywumP7WXeb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "fffwEJ61ZJ47ff0VPNqUDL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 426}, "_children": [{"__id__": 617}], "_active": true, "_components": [{"__id__": 633}, {"__id__": 635}, {"__id__": 637}, {"__id__": 639}, {"__id__": 642}, {"__id__": 644}, {"__id__": 646}], "_prefab": {"__id__": 648}, "_lpos": {"__type__": "cc.Vec3", "x": -127, "y": 164, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 616}, "_children": [{"__id__": 618}, {"__id__": 624}], "_active": true, "_components": [{"__id__": 630}], "_prefab": {"__id__": 632}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 617}, "_children": [], "_active": true, "_components": [{"__id__": 619}, {"__id__": 621}], "_prefab": {"__id__": 623}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": true, "__prefab": {"__id__": 620}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fAf9loTVGxpni21MAqWZq"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 618}, "_enabled": true, "__prefab": {"__id__": 622}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "36SAuG+NJFNpJG8h4QOfZ4"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64FEMsCFNHcoG6kS9GqOVj", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 617}, "_children": [], "_active": true, "_components": [{"__id__": 625}, {"__id__": 627}], "_prefab": {"__id__": 629}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 624}, "_enabled": true, "__prefab": {"__id__": 626}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5dPUBiqpdHy5q12qSQdjWY"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 624}, "_enabled": true, "__prefab": {"__id__": 628}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0bei1otshHOLuPcTthxrlF"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "0cr4WL2qZGt5Fv4Vk+LZXW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 617}, "_enabled": true, "__prefab": {"__id__": 631}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "16I/xj8rlKV6vC6Ac5VZyu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "b8U+tYQdtIf6xDKCPvQPD8", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 634}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "2eI5WeEzxKr53uNg3heCd3"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 636}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "50LwWVbi5LUJPyAFb6glEi"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 638}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -127, "y": 164}, "collideConnected": false, "connectedBody": {"__id__": 472}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "96KdmKSNNLp6o/6rhHwYmz"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 640}, "clickEvents": [{"__id__": 641}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7abnE2Zw1FLJUxRu/B/6tO"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 616}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 643}, "bolt": {"__id__": 627}, "thread": {"__id__": 621}, "joint": {"__id__": 637}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cfQjZENlDSLrLvkc/0HVD"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 645}, "target": {"__id__": 617}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d1rf6CxYNIbq518yEppCOJ"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 616}, "_enabled": true, "__prefab": {"__id__": 647}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "efSpAYRh1P9qcD+DiIg8cu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "edZqL7SvpMTrleif+mC7G/", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 650}, "_contentSize": {"__type__": "cc.Size", "width": 576, "height": 526}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b0xDqoRvZD+KRo+WWbeZOM"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 652}, "img": {"__id__": 446}, "shadow": {"__id__": 431}, "body": {"__id__": 653}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "eaHj/hElxH37nB7AVA3Gjr"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 426}, "_enabled": true, "__prefab": {"__id__": 654}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -17, "y": 263}, {"__type__": "cc.Vec2", "x": -97, "y": 246}, {"__type__": "cc.Vec2", "x": -169, "y": 208}, {"__type__": "cc.Vec2", "x": -224, "y": 155}, {"__type__": "cc.Vec2", "x": -269, "y": 78}, {"__type__": "cc.Vec2", "x": -288, "y": -11}, {"__type__": "cc.Vec2", "x": -279, "y": -95}, {"__type__": "cc.Vec2", "x": -253, "y": -164}, {"__type__": "cc.Vec2", "x": -198, "y": -235}, {"__type__": "cc.Vec2", "x": -165, "y": -260}, {"__type__": "cc.Vec2", "x": -136, "y": -261}, {"__type__": "cc.Vec2", "x": -115, "y": -245}, {"__type__": "cc.Vec2", "x": -97, "y": -213}, {"__type__": "cc.Vec2", "x": -93, "y": -188}, {"__type__": "cc.Vec2", "x": -100, "y": -165}, {"__type__": "cc.Vec2", "x": -137, "y": -126}, {"__type__": "cc.Vec2", "x": -157, "y": -92}, {"__type__": "cc.Vec2", "x": -168, "y": -53}, {"__type__": "cc.Vec2", "x": -169, "y": -13}, {"__type__": "cc.Vec2", "x": -161, "y": 31}, {"__type__": "cc.Vec2", "x": -138, "y": 74}, {"__type__": "cc.Vec2", "x": -105, "y": 108}, {"__type__": "cc.Vec2", "x": -66, "y": 131}, {"__type__": "cc.Vec2", "x": -12, "y": 144}, {"__type__": "cc.Vec2", "x": 56, "y": 135}, {"__type__": "cc.Vec2", "x": 109, "y": 105}, {"__type__": "cc.Vec2", "x": 146, "y": 61}, {"__type__": "cc.Vec2", "x": 167, "y": 5}, {"__type__": "cc.Vec2", "x": 165, "y": -66}, {"__type__": "cc.Vec2", "x": 147, "y": -111}, {"__type__": "cc.Vec2", "x": 106, "y": -157}, {"__type__": "cc.Vec2", "x": 93, "y": -185}, {"__type__": "cc.Vec2", "x": 98, "y": -215}, {"__type__": "cc.Vec2", "x": 117, "y": -247}, {"__type__": "cc.Vec2", "x": 134, "y": -260}, {"__type__": "cc.Vec2", "x": 151, "y": -263}, {"__type__": "cc.Vec2", "x": 174, "y": -255}, {"__type__": "cc.Vec2", "x": 206, "y": -227}, {"__type__": "cc.Vec2", "x": 242, "y": -182}, {"__type__": "cc.Vec2", "x": 277, "y": -104}, {"__type__": "cc.Vec2", "x": 288, "y": -27}, {"__type__": "cc.Vec2", "x": 282, "y": 32}, {"__type__": "cc.Vec2", "x": 266, "y": 85}, {"__type__": "cc.Vec2", "x": 234, "y": 142}, {"__type__": "cc.Vec2", "x": 197, "y": 185}, {"__type__": "cc.Vec2", "x": 151, "y": 220}, {"__type__": "cc.Vec2", "x": 99, "y": 245}, {"__type__": "cc.Vec2", "x": 55, "y": 257}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "06tMUNtg5IoIxfmsytpmXw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "3a6TUgHMhGfqGTrQGSPqhh", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "801", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 1}, "_children": [{"__id__": 657}, {"__id__": 673}, {"__id__": 679}, {"__id__": 714}, {"__id__": 747}], "_active": true, "_components": [{"__id__": 780}, {"__id__": 702}, {"__id__": 782}, {"__id__": 784}], "_prefab": {"__id__": 786}, "_lpos": {"__type__": "cc.Vec3", "x": 240, "y": -47.5, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "mask<PERSON><PERSON>ow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 656}, "_children": [{"__id__": 658}], "_active": true, "_components": [{"__id__": 664}, {"__id__": 666}, {"__id__": 668}, {"__id__": 670}], "_prefab": {"__id__": 672}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "shadow", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 657}, "_children": [], "_active": true, "_components": [{"__id__": 659}, {"__id__": 661}], "_prefab": {"__id__": 663}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -10, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 658}, "_enabled": true, "__prefab": {"__id__": 660}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "74DvhpEexHjrcVEZsdJVD0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 658}, "_enabled": true, "__prefab": {"__id__": 662}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 0, "g": 0, "b": 0, "a": 100}, "_spriteFrame": {"__uuid__": "00a184a6-acb1-4a78-bf06-237937778d02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89XpKSUaZCcYpQfjGNNUlo"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "a5UP6/SCJLcoUvj2dg2un+", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 657}, "_enabled": true, "__prefab": {"__id__": 665}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "b7UePeuwdOSbnoM0bQs6nv"}, {"__type__": "cc.Mask", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 657}, "_enabled": true, "__prefab": {"__id__": 667}, "_type": 3, "_inverted": true, "_segments": 64, "_alphaThreshold": 0.1, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "37oZpUtA5PW4zj4UlPRu2F"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 657}, "_enabled": true, "__prefab": {"__id__": 669}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "00a184a6-acb1-4a78-bf06-237937778d02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "75DfFu8k5DoYhEehq5P3sa"}, {"__type__": "cc.Widget", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 657}, "_enabled": true, "__prefab": {"__id__": 671}, "_alignFlags": 45, "_target": null, "_left": 0, "_right": 0, "_top": 0, "_bottom": 0, "_horizontalCenter": 0, "_verticalCenter": 0, "_isAbsLeft": true, "_isAbsRight": true, "_isAbsTop": true, "_isAbsBottom": true, "_isAbsHorizontalCenter": true, "_isAbsVerticalCenter": true, "_originalWidth": 220, "_originalHeight": 220, "_alignMode": 2, "_lockFlags": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "62KUm+ifdGLZWDZ8Ang7jb"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "8c+VVDFPJExaay/TRUJBe2", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "img", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 656}, "_children": [], "_active": true, "_components": [{"__id__": 674}, {"__id__": 676}], "_prefab": {"__id__": 678}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 673}, "_enabled": true, "__prefab": {"__id__": 675}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "02Z4e+Y3BB9qJZzfcuzoXD"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 673}, "_enabled": true, "__prefab": {"__id__": 677}, "_customMaterial": {"__uuid__": "41b52b27-0049-42bd-8052-73b97eb3decc", "__expectedType__": "cc.Material"}, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 166, "g": 201, "b": 162, "a": 255}, "_spriteFrame": {"__uuid__": "00a184a6-acb1-4a78-bf06-237937778d02@f9941", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f5aoAGSrpNwYwXvJ3RSpxy"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "498vrWZZJG8qlswXcM+J4b", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 656}, "_children": [{"__id__": 680}], "_active": true, "_components": [{"__id__": 696}, {"__id__": 698}, {"__id__": 700}, {"__id__": 704}, {"__id__": 707}, {"__id__": 709}, {"__id__": 711}], "_prefab": {"__id__": 713}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 679}, "_children": [{"__id__": 681}, {"__id__": 687}], "_active": true, "_components": [{"__id__": 693}], "_prefab": {"__id__": 695}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": true, "_components": [{"__id__": 682}, {"__id__": 684}], "_prefab": {"__id__": 686}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 681}, "_enabled": true, "__prefab": {"__id__": 683}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "deYYeXm+dDOZWeTpLvCLyF"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 681}, "_enabled": true, "__prefab": {"__id__": 685}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aadfvi0uJCBrVOGUC4nS61"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "32NTxp+apO95ANdUr6FA3C", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 680}, "_children": [], "_active": true, "_components": [{"__id__": 688}, {"__id__": 690}], "_prefab": {"__id__": 692}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 689}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "8490CE0exHqpkgGw2Y95V5"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 687}, "_enabled": true, "__prefab": {"__id__": 691}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "49+pdDgdZBMZYzxBEub5Tz"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "ade6gMTe5BpZOzCYx294Co", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 680}, "_enabled": true, "__prefab": {"__id__": 694}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "954hRQHFdIG5mlmkG6cyo9"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "185YSNn1dDoKIQVgTGu8IV", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 697}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3cqgEXhzVHTrv+Y06qFSUr"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 699}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "82IJKoeHdKpoSqEl6T9cqC"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 701}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 702}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "89AXsNZvtATZk9aj+n+Uo8"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 656}, "_enabled": true, "__prefab": {"__id__": 703}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 2, "_allowSleep": true, "_gravityScale": 3, "_linearDamping": 0, "_angularDamping": 0.5, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1c5Bnik2FEhrOGygV9+Cd9"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 705}, "clickEvents": [{"__id__": 706}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "479CkWCPREjKQI9xWP8P0v"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 679}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 708}, "bolt": {"__id__": 690}, "thread": {"__id__": 684}, "joint": {"__id__": 700}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "60eQgRaWhECr+zvYMw0Ii3"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 710}, "target": {"__id__": 680}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f8Jhrh75VCz65PWIaJz2my"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 679}, "_enabled": true, "__prefab": {"__id__": 712}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "0aWlEzUnZIBqmCsKmeCbdE"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "d6naUt7CxPjKtFDkCzvwLW", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 656}, "_children": [{"__id__": 715}], "_active": true, "_components": [{"__id__": 731}, {"__id__": 733}, {"__id__": 735}, {"__id__": 737}, {"__id__": 740}, {"__id__": 742}, {"__id__": 744}], "_prefab": {"__id__": 746}, "_lpos": {"__type__": "cc.Vec3", "x": -227.774, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 714}, "_children": [{"__id__": 716}, {"__id__": 722}], "_active": true, "_components": [{"__id__": 728}], "_prefab": {"__id__": 730}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 715}, "_children": [], "_active": true, "_components": [{"__id__": 717}, {"__id__": 719}], "_prefab": {"__id__": 721}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 716}, "_enabled": true, "__prefab": {"__id__": 718}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94nvNS2P1MYZ6TRb3YxAZa"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 716}, "_enabled": true, "__prefab": {"__id__": 720}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "3aHKiSPlRIxqFHfRWhHuu3"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "e5TeEIte1GiZ8IA+YuUvyp", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 715}, "_children": [], "_active": true, "_components": [{"__id__": 723}, {"__id__": 725}], "_prefab": {"__id__": 727}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 722}, "_enabled": true, "__prefab": {"__id__": 724}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "baI+yYfKZEWIrNNP93TOsS"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 722}, "_enabled": true, "__prefab": {"__id__": 726}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f4Kvn456RHa7FQGXwYFrJw"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6bSdqdxnhChpEbmIdDaFhy", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 715}, "_enabled": true, "__prefab": {"__id__": 729}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d2vo7oXL1H8oc9pyQid7Eu"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "10K0cWvZtCmYdM1EWoP8kk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 732}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4fs+HpZ/hMwZYDMw072WAi"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 734}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "7eli43oEhPnImziNeKkYf1"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 736}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": -227.774, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 702}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "5cx8QR7/hD8q8XWMAEKihN"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 738}, "clickEvents": [{"__id__": 739}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "94fnbqyUVHUJibgCTalTTA"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 714}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 741}, "bolt": {"__id__": 725}, "thread": {"__id__": 719}, "joint": {"__id__": 735}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "53ia/GjiBBSpmjZKy4zanZ"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 743}, "target": {"__id__": 715}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "e3c43I+o1C8KXt3v98v15X"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 714}, "_enabled": true, "__prefab": {"__id__": 745}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "dctpRjbJdKcIUSwcvnfd8s"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "4d659A3AVIFafRBFhoPKlJ", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "screw", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 656}, "_children": [{"__id__": 748}], "_active": true, "_components": [{"__id__": 764}, {"__id__": 766}, {"__id__": 768}, {"__id__": 770}, {"__id__": 773}, {"__id__": 775}, {"__id__": 777}], "_prefab": {"__id__": 779}, "_lpos": {"__type__": "cc.Vec3", "x": 224.388, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 0.367}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "target", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 747}, "_children": [{"__id__": 749}, {"__id__": 755}], "_active": true, "_components": [{"__id__": 761}], "_prefab": {"__id__": 763}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.Node", "_name": "thread", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 748}, "_children": [], "_active": true, "_components": [{"__id__": 750}, {"__id__": 752}], "_prefab": {"__id__": 754}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": -20, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 749}, "_enabled": true, "__prefab": {"__id__": 751}, "_contentSize": {"__type__": "cc.Size", "width": 32, "height": 0}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 1}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "1aIffzZSRO47N5OJNFZuIw"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 749}, "_enabled": true, "__prefab": {"__id__": 753}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "ec2a605a-e5d7-4831-9765-d230b52b998a@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 0, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "138WQXMPRFgr/RUcQ5f5J5"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "6dtkYjP/1FHre1k66WoHxL", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.Node", "_name": "bolt", "_objFlags": 0, "__editorExtras__": {}, "_parent": {"__id__": 748}, "_children": [], "_active": true, "_components": [{"__id__": 756}, {"__id__": 758}], "_prefab": {"__id__": 760}, "_lpos": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_lrot": {"__type__": "cc.Quat", "x": 0, "y": 0, "z": 0, "w": 1}, "_lscale": {"__type__": "cc.Vec3", "x": 1, "y": 1, "z": 1}, "_mobility": 0, "_layer": 33554432, "_euler": {"__type__": "cc.Vec3", "x": 0, "y": 0, "z": 0}, "_id": ""}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 755}, "_enabled": true, "__prefab": {"__id__": 757}, "_contentSize": {"__type__": "cc.Size", "width": 82, "height": 85}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afL9fLSN5ANKtnE2MCd1E0"}, {"__type__": "cc.Sprite", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 755}, "_enabled": true, "__prefab": {"__id__": 759}, "_customMaterial": null, "_srcBlendFactor": 2, "_dstBlendFactor": 4, "_color": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_spriteFrame": {"__uuid__": "d2d3f1fb-ad47-41c5-bb8d-8458c2e318d3@c869a", "__expectedType__": "cc.SpriteFrame"}, "_type": 0, "_fillType": 0, "_sizeMode": 1, "_fillCenter": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_fillStart": 0, "_fillRange": 0, "_isTrimmedMode": true, "_useGrayscale": false, "_atlas": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "ceUmPkf/dHPogFYZppat3B"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "bbTvrsGXRKB5umLu5nDjNA", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 748}, "_enabled": true, "__prefab": {"__id__": 762}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "88gSOGk9lBfqPKcZ+5Sfmq"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "55ahV+vWxCsaQa+15xg6TP", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 765}, "_contentSize": {"__type__": "cc.Size", "width": 100, "height": 100}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "bbSN+i9Y1G9rpdbtyFnFja"}, {"__type__": "cc.BoxCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 767}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_size": {"__type__": "cc.Size", "width": 75, "height": 75}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "d4riMkVO1GqZUq0a2rn0qF"}, {"__type__": "cc.HingeJoint2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 769}, "anchor": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "connectedAnchor": {"__type__": "cc.Vec2", "x": 224.388, "y": 0}, "collideConnected": false, "connectedBody": {"__id__": 702}, "_enableLimit": false, "_lowerAngle": 0, "_upperAngle": 0, "_enableMotor": false, "_maxMotorTorque": 1000, "_motorSpeed": 0, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "4bw0gjD15EuqAloIiUEJoK"}, {"__type__": "cc.<PERSON><PERSON>", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 771}, "clickEvents": [{"__id__": 772}], "_interactable": true, "_transition": 0, "_normalColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_hoverColor": {"__type__": "cc.Color", "r": 211, "g": 211, "b": 211, "a": 255}, "_pressedColor": {"__type__": "cc.Color", "r": 255, "g": 255, "b": 255, "a": 255}, "_disabledColor": {"__type__": "cc.Color", "r": 124, "g": 124, "b": 124, "a": 255}, "_normalSprite": null, "_hoverSprite": null, "_pressedSprite": null, "_disabledSprite": null, "_duration": 0.1, "_zoomScale": 1.2, "_target": null, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "fbrkcSQX1Bjr5xgnZZXcP+"}, {"__type__": "cc.ClickEvent", "target": {"__id__": 747}, "component": "", "_componentId": "b561a77epRB6Kt4aIrv0MWT", "handler": "onClick", "customEventData": ""}, {"__type__": "b561a77epRB6Kt4aIrv0MWT", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 774}, "bolt": {"__id__": 758}, "thread": {"__id__": 752}, "joint": {"__id__": 768}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "afu7XvCqlGE6VXtKYFhS2n"}, {"__type__": "82688RFS6tNDaje5Dh3JI1G", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 776}, "target": {"__id__": 748}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f9+kZY6epHkoVH5byGuPKR"}, {"__type__": "cc.RigidBody2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 747}, "_enabled": true, "__prefab": {"__id__": 778}, "enabledContactListener": false, "bullet": false, "awakeOnLoad": true, "_group": 1, "_type": 0, "_allowSleep": true, "_gravityScale": 1, "_linearDamping": 0, "_angularDamping": 0, "_linearVelocity": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_angularVelocity": 0, "_fixedRotation": false, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "aeSGkqE0dMspp5RbK+qvkU"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "64l6APp/9DqabazWIBo6Bk", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.UITransform", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 656}, "_enabled": true, "__prefab": {"__id__": 781}, "_contentSize": {"__type__": "cc.Size", "width": 580, "height": 167}, "_anchorPoint": {"__type__": "cc.Vec2", "x": 0.5, "y": 0.5}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "58iaXhbV1Epa5eAPBqvm0D"}, {"__type__": "e1b16GIoy9PqJ/M6rwf8bNr", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 656}, "_enabled": true, "__prefab": {"__id__": 783}, "img": {"__id__": 676}, "shadow": {"__id__": 661}, "body": {"__id__": 784}, "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "9dXsSeXstBj6Y5arweofza"}, {"__type__": "cc.PolygonCollider2D", "_name": "", "_objFlags": 0, "__editorExtras__": {}, "node": {"__id__": 656}, "_enabled": true, "__prefab": {"__id__": 785}, "tag": 0, "_group": 1, "_density": 1, "_sensor": false, "_friction": 0.2, "_restitution": 0, "_offset": {"__type__": "cc.Vec2", "x": 0, "y": 0}, "_points": [{"__type__": "cc.Vec2", "x": -197, "y": 83.5}, {"__type__": "cc.Vec2", "x": -262, "y": 74.5}, {"__type__": "cc.Vec2", "x": -290, "y": 33.5}, {"__type__": "cc.Vec2", "x": -286, "y": -45.5}, {"__type__": "cc.Vec2", "x": -270, "y": -68.5}, {"__type__": "cc.Vec2", "x": -244, "y": -81.5}, {"__type__": "cc.Vec2", "x": 234, "y": -83.5}, {"__type__": "cc.Vec2", "x": 262, "y": -74.5}, {"__type__": "cc.Vec2", "x": 279, "y": -59.5}, {"__type__": "cc.Vec2", "x": 290, "y": -24.5}, {"__type__": "cc.Vec2", "x": 287, "y": 42.5}, {"__type__": "cc.Vec2", "x": 273, "y": 65.5}, {"__type__": "cc.Vec2", "x": 240, "y": 81.5}], "_id": ""}, {"__type__": "cc.CompPrefabInfo", "fileId": "f0TLK0/VhC54NEG4ztA2tR"}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "84JqgxPI1EVJXI+yHPZMe6", "instance": null, "targetOverrides": null, "nestedPrefabInstanceRoots": null}, {"__type__": "cc.PrefabInfo", "root": {"__id__": 1}, "asset": {"__id__": 0}, "fileId": "04apSI6J1LFK687egvTObg", "targetOverrides": null}]