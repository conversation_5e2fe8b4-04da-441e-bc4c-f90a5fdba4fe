import { _decorator, AnimationComponent, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Tutorial')
export class Tutorial extends Component {

    @property(AnimationComponent) public animation: AnimationComponent = null;

    protected onLoad(): void {
        // this.node.on(Node.EventType.TOUCH_START, this.onTouchStart, this);

    }

    private onTouchStart(event: any): void {

        console.log("Touch Start");
        if (!this.animation.node.active) return;

        console.log("Touch Start 2");
        this.animation.stop();
        this.animation.node.active = false;
    }

    public play(): void {
        this.animation.node.active = true;
        this.animation.play();
    }
}


