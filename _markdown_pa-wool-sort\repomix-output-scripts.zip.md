This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

# File Summary

## Purpose
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

## File Format
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Multiple file entries, each consisting of:
  a. A header with the file path (## File: path/to/file)
  b. The full contents of the file in a code block

## Usage Guidelines
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

## Notes
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)

## Additional Info

# Directory Structure
```
audio/
  AudioManager.ts/
    AudioManager.ts
  AudioManager.ts.meta/
    AudioManager.ts.meta
  MyAudioSource.ts/
    MyAudioSource.ts
  MyAudioSource.ts.meta/
    MyAudioSource.ts.meta
audio.meta/
  audio.meta
BoxHolder.ts/
  BoxHolder.ts
BoxHolder.ts.meta/
  BoxHolder.ts.meta
ChildCount.ts/
  ChildCount.ts
ChildCount.ts.meta/
  ChildCount.ts.meta
Fake3D.ts/
  Fake3D.ts
Fake3D.ts.meta/
  Fake3D.ts.meta
flow/
  FlowManager.ts/
    FlowManager.ts
  FlowManager.ts.meta/
    FlowManager.ts.meta
  FlowUnit.ts/
    FlowUnit.ts
  FlowUnit.ts.meta/
    FlowUnit.ts.meta
  intro/
    GameIntro1.ts/
      GameIntro1.ts
    GameIntro1.ts.meta/
      GameIntro1.ts.meta
  intro.meta/
    intro.meta
  outtro/
    BaseGameOuttro.ts/
      BaseGameOuttro.ts
    BaseGameOuttro.ts.meta/
      BaseGameOuttro.ts.meta
    GameOuttro1.ts/
      GameOuttro1.ts
    GameOuttro1.ts.meta/
      GameOuttro1.ts.meta
    GameOuttroNone.ts/
      GameOuttroNone.ts
    GameOuttroNone.ts.meta/
      GameOuttroNone.ts.meta
  outtro.meta/
    outtro.meta
  tutorial/
    Tutorial.ts/
      Tutorial.ts
    Tutorial.ts.meta/
      Tutorial.ts.meta
  tutorial.meta/
    tutorial.meta
flow.meta/
  flow.meta
GameBox.ts/
  GameBox.ts
GameBox.ts.meta/
  GameBox.ts.meta
GameCamera.ts/
  GameCamera.ts
GameCamera.ts.meta/
  GameCamera.ts.meta
GameConfig.ts/
  GameConfig.ts
GameConfig.ts.meta/
  GameConfig.ts.meta
GameLayer.ts/
  GameLayer.ts
GameLayer.ts.meta/
  GameLayer.ts.meta
GameMap.ts/
  GameMap.ts
GameMap.ts.meta/
  GameMap.ts.meta
GameObject.ts/
  GameObject.ts
GameObject.ts.meta/
  GameObject.ts.meta
i18n/
  LocalizationLabel.ts/
    LocalizationLabel.ts
  LocalizationLabel.ts.meta/
    LocalizationLabel.ts.meta
  StringManager.ts/
    StringManager.ts
  StringManager.ts.meta/
    StringManager.ts.meta
i18n.meta/
  i18n.meta
MapGenerator.ts/
  MapGenerator.ts
MapGenerator.ts.meta/
  MapGenerator.ts.meta
miscs/
  AnimationCallback.ts/
    AnimationCallback.ts
  AnimationCallback.ts.meta/
    AnimationCallback.ts.meta
  AutoFill.ts/
    AutoFill.ts
  AutoFill.ts.meta/
    AutoFill.ts.meta
  HandRing.ts/
    HandRing.ts
  HandRing.ts.meta/
    HandRing.ts.meta
  LabelAnim.ts/
    LabelAnim.ts
  LabelAnim.ts.meta/
    LabelAnim.ts.meta
  LabelLevel.ts/
    LabelLevel.ts
  LabelLevel.ts.meta/
    LabelLevel.ts.meta
  vfx/
    Remarkable.ts/
      Remarkable.ts
    Remarkable.ts.meta/
      Remarkable.ts.meta
    VFX.ts/
      VFX.ts
    VFX.ts.meta/
      VFX.ts.meta
  vfx.meta/
    vfx.meta
miscs.meta/
  miscs.meta
room/
  RoomDecor.ts/
    RoomDecor.ts
  RoomDecor.ts.meta/
    RoomDecor.ts.meta
  RoomLayer.ts/
    RoomLayer.ts
  RoomLayer.ts.meta/
    RoomLayer.ts.meta
  RoomLayerMaterial.ts/
    RoomLayerMaterial.ts
  RoomLayerMaterial.ts.meta/
    RoomLayerMaterial.ts.meta
  RoomLayerSprite.ts/
    RoomLayerSprite.ts
  RoomLayerSprite.ts.meta/
    RoomLayerSprite.ts.meta
room.meta/
  room.meta
Screenshake.ts/
  Screenshake.ts
Screenshake.ts.meta/
  Screenshake.ts.meta
Screw.ts/
  Screw.ts
Screw.ts.meta/
  Screw.ts.meta
ShapeGenerator.ts/
  ShapeGenerator.ts
ShapeGenerator.ts.meta/
  ShapeGenerator.ts.meta
StaticResources.ts/
  StaticResources.ts
StaticResources.ts.meta/
  StaticResources.ts.meta
test/
  TestCamera.ts/
    TestCamera.ts
  TestCamera.ts.meta/
    TestCamera.ts.meta
  TestCollider2D.ts/
    TestCollider2D.ts
  TestCollider2D.ts.meta/
    TestCollider2D.ts.meta
test.meta/
  test.meta
TopLayer.ts/
  TopLayer.ts
TopLayer.ts.meta/
  TopLayer.ts.meta
```

# Files

## File: audio/AudioManager.ts/AudioManager.ts
```typescript
import { AudioSource } from "cc";

export default class AudioManager{
    static sfx = {};

    static addEffect(key, audio:AudioSource){
        this.sfx[key] = audio;
    }

    static play(key){
        this.sfx[key]?.play();
    }
}
```

## File: audio/AudioManager.ts.meta/AudioManager.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "caf695a4-6c92-4c6b-b984-72d4c4c7bd01",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: audio/MyAudioSource.ts/MyAudioSource.ts
```typescript
import { _decorator, AudioSource, Component, Node } from 'cc';
import AudioManager from './AudioManager';
const { ccclass, property } = _decorator;

@ccclass('MyAudioSource')
export class MyAudioSource extends AudioSource {
    @property
    audioKey:string = "";
    start() {
        AudioManager.addEffect(this.audioKey||"sfx", this);
    }

    update(deltaTime: number) {
        
    }
}
```

## File: audio/MyAudioSource.ts.meta/MyAudioSource.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "aa0ac431-e598-4b5e-b046-77fb573d6e4e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: audio.meta/audio.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "730d0916-540c-419c-897c-bd6e067a873f",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: BoxHolder.ts/BoxHolder.ts
```typescript
import { _decorator, Component, instantiate, Node } from 'cc';
import { GameBox } from './GameBox';
import { Screw } from './Screw';
import { GameMap } from './GameMap';
import AudioManager from './audio/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('BoxHolder')
export class BoxHolder extends Component {
    public static i : BoxHolder = null;

    @property(GameBox)
    temp: GameBox = null;
    @property(GameBox)
    box:GameBox = null;
    @property(Node)
    holder:Node = null;

    @property([GameBox])
    queue: GameBox[] = [];

    data : any = null;
    lastBoxColor = -1;
    score = 0;
    gameMap;
    playing;
    start() {
    }
    onLoad(){
        
        BoxHolder.i = this;
    }

    getBoxColor(){
        const keys = Object.keys(this.data);
        if(keys.length == 0){
            const temp = this.lastBoxColor;
            this.lastBoxColor = -1;
            return temp;
        }
        const key = keys[Math.floor(Math.random()*keys.length)];
        this.data[key]--;
        if(this.data[key] <= 0){
            delete this.data[key];
        }
        return key;
    }
    /**
     * 
     * @param data : {id:count}
     */
    init(data, lastBoxColor, firstColor = -1){
        this.playing = true;
        this.score = 0;
        this.data = data;
        this.box.ready = true;
        this.lastBoxColor = lastBoxColor;
        const initCount = 3;
        for(let i = 0; i < Math.max(initCount, this.queue.length); i++){
            if(i >= this.queue.length){
                const node = instantiate(this.temp.node);
                node.active = true;
                node.setParent(this.holder || this.node);
                this.queue.push(node.getComponent(GameBox));
                this.queue[i].owner = this;
            }
            if(firstColor >= 0 && i == 0){
                this.queue[i].init(firstColor);
            }else{
                this.queue[i].init(this.getBoxColor());
            }
            this.queue[i].wait(i);
        }
    }

    newBox(){
        if(!this.playing)
            return;
        const box = this.queue[0];
        box.ready = false;
        box.closeLid(this.getBoxColor()).then(()=>{
            this.queue.shift();
            for(let i = 1; i < this.queue.length; i++){
                this.queue[i].wait(i);
            }
            this.queue[0].wait(0).then(()=>{
                this.queue.push(box);

                let c = this.queue[0].screws.length;
                for(let i in this.box.screws){
                    if(c >= 3){
                        break;
                    }
                    const screw = this.box.screws[i];
                    if(screw && screw.boltId == this.queue[0].boxId){
                        if(!this.box.slots[i])
                            debugger;
                        this.box.slots[i]["occupied"] = false;
                        this.box.screws[i] = null;
                        screw.onClick();
                        c++;
                    }
                }

                if(this.box.fulled){
                    this.playing = false;
                    this.gameMap.lose();
                }
            });
        });
    }
    checkFullBox(box){
        if(!this.playing)
            return;
        if(!box.fulled)
            return;
        if(this.queue[0].ready){
            if(box == this.queue[0]){
                this.newBox();
            }else if(box == this.box){
                this.playing = false;
                this.gameMap.lose();
            }
        }
    }
    onCollectBolt(box, screw){
        if(!this.playing)
            return;
        this.gameMap.onCollect();
        if(box != this.box){
            this.score ++;
            if(this.score >= this.gameMap.totalBolt){
                this.gameMap.win();
                this.playing = false;
            }
        }
    }
    getBox(id){
        if(!this.playing)
            return null;
        if(this.queue[0].boxId == id && !this.queue[0].fulled && this.queue[0].ready){
            return this.queue[0];
        }
        return this.box.fulled ? null : this.box;
    }

    update(deltaTime: number) {
        
    }
}
```

## File: BoxHolder.ts.meta/BoxHolder.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "07739c80-8de4-4f88-bc00-96837fd34913",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: ChildCount.ts/ChildCount.ts
```typescript
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('ChildCount')
export class ChildCount extends Component {
    @property
    get count(){
        return false;
    }
    set count(v){
        console.log("children count: " + this.node.children.length);
    }

    @property
    get preventDuplicate(){
        return false;
    }
    set preventDuplicate(v){
        const arr = [];
        for(let i = 0; i < this.node.children.length;){
            const node = this.node.children[i];
            if(arr.indexOf(node.name) >= 0){
                this.node.removeChild(node);
            }else{
                arr.push(node.name)
                i++;
            }
        }
    }

    start() {

    }

    update(deltaTime: number) {
        
    }
}
```

## File: ChildCount.ts.meta/ChildCount.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "ad205428-8f58-45cf-bbe6-950c3f164466",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: Fake3D.ts/Fake3D.ts
```typescript
import { _decorator, Component, Node, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Fake3D')
export class Fake3D extends Component {

    @property(Node)
    target: Node = null;
    _z:number = 0;
    @property({step:1, max:1000, min:-1000, slide:true})
    get z(){
        return this._z;
    }
    set z(v){
        this._z = v;
        this.refreshZ();
    }
    
    refreshZ(){
        this.target.position = v3(0, this.z*0.15, 999);
        
        const maxHeight = 2000;
        const scale = Math.max(0, (this.z+maxHeight)/maxHeight);
        this.target.scale = v3(scale, scale, scale);
    }

    start() {

    }

    update(deltaTime: number) {
        
    }
}
```

## File: Fake3D.ts.meta/Fake3D.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "82688445-4bab-4d0d-a8de-e43877248d46",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/FlowManager.ts/FlowManager.ts
```typescript
import { _decorator, AudioSource, Component, instantiate, Node, Prefab } from 'cc';
import { FlowUnit } from './FlowUnit';
const { ccclass, property } = _decorator;

const mraid = window['mraid'] || null;

@ccclass('FlowManager')
export class FlowManager extends Component {
    public static isLoaded = false;
    public static instance : FlowManager = null;

    @property(AudioSource)
    bgm:AudioSource = null;

    @property
    get unshift(){return false;}
    set unshift(v){this.flows.unshift(null)};
    @property
    get shift(){return false;}
    set shift(v){this.flows.shift()};
    @property
    get push(){return false;}
    set push(v){this.flows.push(null)};
    @property
    get pop(){return false;}
    set pop(v){this.flows.pop()};
    @property([Prefab])
    flows:Prefab[] = [];
    
    @property([Node])
    ui:Node[] = [];
    @property([Node])
    layer:Node[] = [];

    units:FlowUnit[] = [];

    _step = -1;
    get step(){
        return this._step;
    }
    set step(v){
        this._step = (v%this.flows.length);

        if(this.units.length <= this.step){
            for(let i = this.units.length-1; i < this.step; i++){
                this.units.push(null);
            }
        }
        if(!this.units[this.step])
            this.preload(this.step, this.data[this.step]);
        this.units[this.step].show(this.data[this.step]);
        this.preload(this.step+1, null, this.units[this.step].preloadNextStep);
    };

    onLoad(){
        FlowManager.instance = this;
        window['gameReady'] && window['gameReady']();
        this.ui.forEach(item=>{
            item.active = false;
        })
    }

    start() {
        this.waitSettingUp(window['adsChannels']);
    }

    waitSettingUp(ad_network){
        switch(ad_network){
            case "Unity":{
                console.log('init case Unity');
                mraid ? this.initUnity() : this.init();
                break;
            }
            case "Mintegral":{
                //call from window["gameStart"]
                if(FlowManager.isLoaded){
                    console.log('init case Mintegral lately');
                    this.init();
                }
                break;
            }
            default:{
                console.log('init case Default');
                this.init();
                break;
            }
        }
    }

    initUnity(){
        if(!!mraid){
            this.init();
            return;
        }
        mraid.addEventListener('ready', ()=>{
            if (mraid?.isViewable()) {
                this.init();
            }else{
                mraid?.addEventListener('viewableChange',()=>{
                    this.init();
                });
            }
        });
    }
    init(){
        this.step = 0;
        this.bgm.play();
        this.ui.forEach(item=>{
            item.active = true;
        })
    }

    data = {};
    nextStep(keepPrev?, data?){
        this.preload(this.step+1, data, this.units[this.step].preloadNextStep);

        if(!keepPrev)
            this.units[this.step].hide().then(()=>{
                this.step++;
            });
        else{
            this.step++;
        }
    }

    preload(_step, data, show?){
        const step = _step%this.flows.length;
        if(!this.flows[step])
            return;
        this.data[this.step+1] = data;
        if(!this.units[step]){
            this.units[step] = instantiate(this.flows[step]).getComponent(FlowUnit);
            const layer = Math.max(0, Math.min(this.layer.length-1, this.units[step].layer));
            this.units[step].node.setParent(this.layer[layer]);
            if(this.units[step].layer == 0){
                this.units[step].node.setSiblingIndex(0);
            }
            this.units[step].manager = this;
        }
        if(!this.units[step].isValid)
            return;
        this.units[step].node.active = !!show;
        this.units[step].refreshOnLoad = !!show;
        this.units[step].preload(this.data[step]);
    }

    // loaded = false;
    update(deltaTime: number) {
        // if(!this.loaded && window["advChannels"] != "__adv_channels"+"_adapter__"){
        //     this.loaded = true;
        //     this.waitSettingUp(window["advChannels"]);
        // }
    }
}

window['advChannels'] = "{{__adv_channels_adapter__}}";

window["gameStart"] = function(){
    if(!FlowManager.instance){
        console.log('init case Mintegral early');
        FlowManager.isLoaded = true;
    }else{
        console.log('init case Mintegral lately');
        FlowManager.instance.init();
    }
};
window["gameClose"] = function(){
    if(FlowManager.instance){
        FlowManager.instance.bgm.stop();
    }
};
```

## File: flow/FlowManager.ts.meta/FlowManager.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "11ff7306-cf6c-4d5d-bfb5-342fcc1dfed2",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/FlowUnit.ts/FlowUnit.ts
```typescript
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('FlowUnit')
export class FlowUnit extends Component {
    @property
    layer = 0;
    @property
    _refreshOnLoad = false;
    @property
    get refreshOnLoad(){
        return this._refreshOnLoad;
    }
    set refreshOnLoad(v){
        this._refreshOnLoad = v;
    }
    @property
    preloadNextStep:boolean = false;

    manager;

    preload(data){

    }
    show(data?){
        this.node.active = true;
    }
    hide(){
        return new Promise(resolve=>{
            this.node.active = false;
            resolve(null);
        });
    }

    nextStep(){
        this.manager?.nextStep();
    }

    start() {

    }

    update(deltaTime: number) {
        
    }
}
```

## File: flow/FlowUnit.ts.meta/FlowUnit.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "ad9b28f4-d367-43b4-92ce-f6c9792b4213",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/intro/GameIntro1.ts/GameIntro1.ts
```typescript
import { _decorator, Component, Node, tween, UIOpacity, v3 } from 'cc';
import { FlowUnit } from '../FlowUnit';
const { ccclass, property } = _decorator;

@ccclass('GameIntro1')
export class GameIntro1 extends FlowUnit {
    @property
    delayNextFlow = -1;
    @property([UIOpacity])
    nodes:UIOpacity[] = [];
    @property(UIOpacity)
    bg:UIOpacity = null;

    start() {
        if(this.delayNextFlow >= 0){
            this.scheduleOnce(this.nextStep, this.delayNextFlow);
        }
    }
    hide(){
        return new Promise(resolve=>{
            const t = 0.5;
            for(let i = 0; i < this.nodes.length; i++){
                const t1 = t*(0.3+i*0.1);
                const t2 = t-t1;
                tween(this.nodes[i].node).to(t1, {scale:v3(1.5, 1.5, 1.5)}).to(t2, {scale:v3()}).start();
                tween(this.nodes[i]).delay(t1).to(t2, {opacity:0}).start();
            }
            tween(this.bg).delay(t*0.8+0.1*this.nodes.length).to(t*0.5, {opacity:0}).call(()=>{
                resolve(null);
                this.node.active = false;
            }).start();
        })
    }

    update(deltaTime: number) {
        
    }
}
```

## File: flow/intro/GameIntro1.ts.meta/GameIntro1.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "480f5928-a07e-4ef2-abd6-fe04b759200e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/intro.meta/intro.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "61e3fc4e-da47-4e31-b609-21d6c1cd7adf",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/outtro/BaseGameOuttro.ts/BaseGameOuttro.ts
```typescript
import { _decorator, Component, Node, sys, tween, UIOpacity } from 'cc';
import { FlowUnit } from '../FlowUnit';
const { ccclass, property } = _decorator;

@ccclass('BaseGameOuttro')
export class BaseGameOuttro extends FlowUnit {

    show(data?){
        this.node.active = true;
        const opacity = this.node.getComponent(UIOpacity);
        if(opacity){
            opacity.opacity = 0;
            tween(opacity).to(0.5, {opacity:255}).start();
        }

        const ad_network = window['advChannels'];
        if(!!ad_network){
            switch(ad_network){
                case "Mintegral":{
                    window['gameEnd'] && window['gameEnd']();
                    return;
                }
            }
        }
    }
   
    cta(){
        const ad_network = window['advChannels'];
        if(!!ad_network){
            switch(ad_network){
                case "Mintegral":{
                    window['install'] && window['install']();
                    return;
                }
                case "Unity":
                case "AppLovin":{
                    let open;
                    if(sys.os == sys.OS.ANDROID){
                        open = window["mraidOpenPlayStore"];
                    }else{
                        open = window["mraidOpenAppStore"];
                    }
                    open?.();
                    return;
                };
            }
        }
        sys.openURL("https://play.google.com/store/apps/details?id=com.legendarylabs.boltbuster&hl=en");
    }
}
```

## File: flow/outtro/BaseGameOuttro.ts.meta/BaseGameOuttro.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "16d73243-e9ea-4d6a-93bd-c0f2f68b3ec8",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/outtro/GameOuttro1.ts/GameOuttro1.ts
```typescript
import { _decorator, Component, Label, Node } from 'cc';
import { FlowUnit } from '../FlowUnit';
import { BaseGameOuttro } from './BaseGameOuttro';
const { ccclass, property } = _decorator;


@ccclass('GameOuttro1')
export class GameOuttro1 extends BaseGameOuttro {
    
    start() {

    }

    update(deltaTime: number) {
        
    }
}
```

## File: flow/outtro/GameOuttro1.ts.meta/GameOuttro1.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "b8e79e4c-159f-4473-b99e-5370fb9277f9",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/outtro/GameOuttroNone.ts/GameOuttroNone.ts
```typescript
import { _decorator, Component, Label, Node } from 'cc';
import { FlowUnit } from '../FlowUnit';
import { BaseGameOuttro } from './BaseGameOuttro';
const { ccclass, property } = _decorator;


@ccclass('GameOuttro1')
export class GameOuttro1 extends BaseGameOuttro {
    
    show(data?){
        this.cta();
    }
}
```

## File: flow/outtro/GameOuttroNone.ts.meta/GameOuttroNone.ts.meta
```
{"ver":"4.0.23","importer":"typescript","imported":true,"uuid":"0c774e6a-748d-4287-89b9-6b911e59b415","files":[],"subMetas":{},"userData":{}}
```

## File: flow/outtro.meta/outtro.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "da9ffbf5-2bdf-4802-a7c7-953bfe0c925a",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/tutorial/Tutorial.ts/Tutorial.ts
```typescript
import { _decorator, Animation, Component, Label, Node, tween, UIOpacity, UITransform, v3 } from 'cc';
import { Screw } from '../../Screw';
import { LabelAnim } from '../../miscs/LabelAnim';
import { FlowManager } from '../FlowManager';
const { ccclass, property } = _decorator;

@ccclass('Tutorial')
export class Tutorial extends Component {
    public static i:Tutorial = null;
    @property
    playOnLoad = false;
    @property
    nextFlow = false;
    @property(Animation)
    hand:Animation = null;
    @property(Screw)
    screw:Screw = null;
    @property(LabelAnim)
    labelAnim:LabelAnim = null;
    @property(Node)
    block:Node = null;

    target : Screw = null;

    onLoad(){
        this.reset();
    }
    protected start(): void {
        Tutorial.i = this;

        this.hand.node.on("clicked", this.playScrew, this);
        this.labelAnim.node.on("full", this.labelFull, this);
        this.labelAnim.node.on("endIdle", this.playHand, this);
        if(!this.playOnLoad){
            this.reset();
        }

        this.scheduleOnce(()=>{
            this.block.active = false;
        }, 1);
    }

    reset(){
        this.hand.node.active = false;
        this.hand.getComponent(Animation).stop();
        this.screw.getComponent(UIOpacity).opacity = 0;
    }

    tutorial(target){
        this.reset();
        this.target = target;
        this.labelAnim.play2(0);
    }
    labelFull(){
        if(this.playOnLoad && this.nextFlow)
            FlowManager.instance.nextStep();
    }
    playHand(){
        this.screw.boltId = this.target.boltId;
        const transform = this.getComponent(UITransform);
        const position = transform.convertToNodeSpaceAR(this.target.getComponent(UITransform).convertToWorldSpaceAR(v3()));
        this.hand.node.position = position;
        
        this.hand.node.active = true;
        this.hand.node.scale = v3(0,0,0);
        tween(this.hand.node).to(0.3, {scale:v3(1,1,1)}, {easing:'backOut'}).call(()=>{
            this.hand.getComponent(Animation).play();
        }).start();
    }
    playScrew(){
        const transform = this.getComponent(UITransform);
        const position = transform.convertToNodeSpaceAR(this.target.getComponent(UITransform).convertToWorldSpaceAR(v3()));
        this.screw.node.position = position;
        this.screw.getComponent(UIOpacity).opacity = 255;
        this.screw.tutorial().then(()=>{
            tween(this.screw.getComponent(UIOpacity)).to(0.5, {opacity:0}).start();
        })
    }
    hide(){
        this.node.active = false;
        this.reset();
    }
}
```

## File: flow/tutorial/Tutorial.ts.meta/Tutorial.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "5b21b0cc-1fa0-4fc1-bb68-022561e7090e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow/tutorial.meta/tutorial.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "58574cc2-1493-434c-9543-3faa583020e5",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: flow.meta/flow.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "3f7393cb-1f80-4d73-9e2f-9a809e3df1d5",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameBox.ts/GameBox.ts
```typescript
import { _decorator, Component, Node, Sprite, tween, v2, v3, view } from 'cc';
import { StaticResources } from './StaticResources';
import AudioManager from './audio/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('GameBox')
export class GameBox extends Component {
    _boxId = 0;
    get boxId(){
        return this._boxId;
    }
    set boxId(v){
        this._boxId = v;
        this.lids.forEach(lid=>{
            lid.spriteFrame = this.boxId >= 0 ? StaticResources.i.boxLid[this.boxId] : null;
        });
        this.imgs.forEach(img=>{
            img.spriteFrame = this.boxId >= 0 ? StaticResources.i.box[this.boxId] : null;
        });
        for(let i = 0; i < this.slots.length; i++){
            this.slots[i].active = this.boxId >= 0;
        }
    }
    @property([Node])
    slots:Node[] = [];
    @property(Node)
    holder:Node = null;
    @property(Node)
    lid:Node = null;
    @property([Sprite])
    lids:Sprite[] = [];
    @property([Sprite])
    imgs:Sprite[] = [];

    screws = [];

    owner;//BoxHolder
    ready = false;

    start() {

    }
    init(id){
        this.boxId = id;
        this.screws = []
        this.lid.position = v3(0, 500);
        this.slots.forEach(a=>{
            a["occupied"] = false; 
        });
        this.ready = false;
        this.holder.removeAllChildren();
    }
    wait(index){
        return new Promise(resolve=>{
            this.node.position = v3(-view.getVisibleSize().width*(index+1)/2, 0);
            tween(this.node).to(0.5, {position:v3(-view.getVisibleSize().width*index/2, 0)}).call(()=>{
                this.ready = index == 0;
                resolve(null);
            }).start();
        })
    }

    closeLid(nextColor){
        return new Promise((resolve, reject)=>{
            AudioManager.play("close");
            this.ready = false;
            tween(this.lid).to(0.3, {position:v3(0, 0)}).call(()=>{
                tween(this.node).by(0.05, {position:v3(0,-15,0)}).by(0.05, {position:v3(0,15,0)})
                .to(0.1, {position:v3(-50, 0)}).call(()=>{
                    resolve(null);
                    AudioManager.play("newBox");
                })
                .to(0.5, {position:v3(700, 0)}).call(()=>{
                        this.init(nextColor);
                        this.wait(2);                        
                    }).start();
            }).start();
        });
    }

    getSlot(){
        for(let i = 0; i < this.slots.length; i++){
            if(!this.slots[i]["occupied"])
                return this.slots[i];
        }
        return null;
    }

    get full(){
        return this.screws.length >= this.slots.length;
    }
    get fulled(){
        for(let i in this.screws){
            if(!this.screws[i] || !this.screws[i].collected)
                return false;
        }
        return this.full;
    }

    putScrew(screw){
        for(let i in this.screws){
            if(!this.screws[i]){
                this.screws[i] = screw;
                return;
            }
        }
        if(this.screws.length < this.slots.length)
            this.screws.push(screw);
    }
    
    update(deltaTime: number) {
        
    }
}
```

## File: GameBox.ts.meta/GameBox.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "5640cb04-b7eb-406a-ae87-f280d8284706",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameCamera.ts/GameCamera.ts
```typescript
import { _decorator, Camera, color, Component, Graphics, Node, RenderTexture, Sprite, SpriteFrame, Texture2D, UITransform, v2, v3, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameCamera')
export class GameCamera extends Component {
    public static i:GameCamera = null;

    @property(Camera)
    camera1: Camera = null;
    @property(Camera)
    camera2: Camera = null;

    @property([Sprite])
    sprites: Sprite[] = [];

    @property([Sprite])
    renders: Sprite[] = [];

    start() {
        GameCamera.i = this;

        if (!this.camera1.targetTexture) {
            const texture1 = new RenderTexture();
            texture1.reset(view.getDesignResolutionSize());
            this.camera1.targetTexture = texture1;

            const frame = new SpriteFrame();
            frame.texture = texture1;
            this.renders[0].spriteFrame = frame;
        }
        if (!this.camera2.targetTexture) {
            const texture2 = new RenderTexture();
            texture2.reset(view.getDesignResolutionSize());
            this.camera2.targetTexture = texture2;

            const frame = new SpriteFrame();
            frame.texture = texture2;
            this.renders[1].spriteFrame = frame;
        }
        
        this.node.on(Node.EventType.TOUCH_START, this.touchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.touchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.touchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.touchCancel, this);
    }

    checkBoltBlock(boltImg, otherImgs){
        const texture2 = new RenderTexture();
        texture2.reset(view.getDesignResolutionSize());
        this.camera2.targetTexture = texture2;
        
        const texture1 = new RenderTexture();
        texture1.reset(view.getDesignResolutionSize());
        this.camera1.targetTexture = texture1;

        return new Promise(resolve=>{
            
            if(otherImgs.length == 0){
                resolve(false);
                return;
            }                
            const bLayer = boltImg.node.layer;
            boltImg.node.layer = 1 << 1;

            let oLayer = 0;
            if(otherImgs.length > 0){
                oLayer = otherImgs[0].node.layer;
                for(let i in otherImgs){
                    otherImgs[i].node.layer = 1;
                }
            }
                        
            this.scheduleOnce(()=>{
                const transform : UITransform = boltImg.getComponent(UITransform);
                const w = transform.width;
                const h = transform.height;
                const position = transform.convertToWorldSpaceAR(v3(0,0,0));
                const x = position.x - w/2;
                const y = position.y - h/2 - (view.getVisibleSize().height - view.getDesignResolutionSize().height)/2;
                const buffer = this.camera1.targetTexture.readPixels(x, y, w, h);
                const buffer2 = this.camera2.targetTexture.readPixels(x, y, w, h);

                // this.sprites[0].color = color(buffer[0], buffer[1], buffer[2], 255);
                // this.sprites[1].color = color(buffer2[0], buffer2[1], buffer2[2], 255);

                let f = false;
                for(let i = 0; i < buffer.length; i+=4){
                    const f1 = buffer2[i+3] > 200 ||  buffer2[i+3] > 100 && this.checkNear(this.nears(i, w), buffer2, 100) > 0.5;
                    if(f1){
                        const f2 = buffer[i+3] > 200 ||  buffer[i+3] > 100 && this.checkNear(this.nears(i, w), buffer, 100) > 0.5;
                        if(f2){
                            this.sprites[1].color = color(buffer2[i+0], buffer2[i+1], buffer2[i+2], 255);
                            f = true;
                            break;
                        }
                    }
                }
                if(!f)
                    this.sprites[1].color = color(100,100,100, 255);
                resolve(f);

                
                boltImg.node.layer = bLayer;
                if(otherImgs.length > 0){
                    for(let i in otherImgs){
                        otherImgs[i].node.layer = oLayer;
                    }
                }
            })
        });
        
    }

    near(i, w, ox, oy){
        const x = (i/4)%w + ox;
        const y = Math.floor((i/4)/w) + oy;
        return (x*4 + y*4*w);
    }
    nears(i, w){
        return [
            this.near(i, w, 1, 1),
            this.near(i, w, 0, 1),
            this.near(i, w, -1, 1),
            this.near(i, w, -1, 0),
            this.near(i, w, 1, 0),
            this.near(i, w, -1, -1),
            this.near(i, w, 0, -1),
            this.near(i, w, 1, -1),
        ];
    }
    checkNear(is, b, o){
        let c = 0;
        for(let i in is){
            if (b[is[i] + 3] > o)
                c++;
        }
        return c/is.length;
    }

    update(deltaTime: number) {
        
    }

    touching = false;
    touchStart(e){
        this.touching = true;
        this.process(e.getUILocation())
    }
    touchMove(e) {
        this.process(e.getUILocation())
    }
    process(position) {
        let f = !!this.camera1.targetTexture && !!this.camera2.targetTexture;

        // const p = this.cursor.parent.getComponent(UITransform).convertToNodeSpaceAR(v3(position.x, position.y, 0));
        // this.cursor.position = p;
        
        this.scheduleOnce(()=>{
            const w = 1;// this.cursor.getComponent(UITransform).width;
            const h = 1;// this.cursor.getComponent(UITransform).height;
            // const b1 = this.camera1.targetTexture.readPixels(position.x, position.y, 1, 1);
            const x = position.x - w/2;
            const y = position.y - h/2 - (view.getVisibleSize().height - view.getDesignResolutionSize().height)/2;
            const b1 = this.camera1.targetTexture.readPixels(x, y, w, h);
            const b2 = this.camera2.targetTexture.readPixels(x, y, w, h);
            // if(b1[3] > 0 && b2[3] > 0){
                
            // }
            this.sprites[0].color = color(b1[0], b1[1], b1[2], b1[3]);
            this.sprites[1].color = color(b2[0], b2[1], b2[2], b2[3]);
            // return;

            const near = function(i, w, ox, oy){
                const x = (i/4)%w + ox;
                const y = Math.floor((i/4)/w) + oy;
                return (x*4 + y*4*w);
            }
            const nears = function(i, w){
                return [
                    near(i, w, 1, 1),
                    near(i, w, 0, 1),
                    near(i, w, -1, 1),
                    near(i, w, -1, 0),
                    near(i, w, 1, 0),
                    near(i, w, -1, -1),
                    near(i, w, 0, -1),
                    near(i, w, 1, -1),
                ];
            }
            const minA = 50;
            const checkNear = function(is){
                let c = 0;
                for(let i in is){
                    if (b1[is[i] + 3] > minA && b2[is[i] + 3] > minA)
                        c++;
                }
                return c/is.length;
            }

            for(let i = 0; i < b1.length; i+= 4){
                if(b1[i+3] > minA && b2[i+3] > minA){
                    if(b1[i+3] > 200 && b2[i+3] > 200 || checkNear(nears(i, w)) > 0.5){
                        this.sprites[0].color = color(b1[i + 0], b1[i + 1], b1[i + 2], b1[i + 3]);
                        this.sprites[1].color = color(b2[i + 0], b2[i + 1], b2[i + 2], b2[i + 3]);
                        return;
                    }

                }
            } 
            this.sprites[0].color = color(0,0,0,0);
        })
    }
    touchEnd(){
        this.touching = false;
    }
    touchCancel(){
        this.touchEnd();
    }

}
```

## File: GameCamera.ts.meta/GameCamera.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "a13cbaeb-3be2-4b85-931a-a98cf319130f",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameConfig.ts/GameConfig.ts
```typescript
export default class GameConfig{
    static group = {
        red: {
            id:[0,1],
            color:["#FF92B7","#FDBBA1"]
        },
        yellow: {
            id:[2,3],
            color:["#F5DE67", "#F5DE66"]
        },
        green: {
            id:[4,5],
            color:["#A6E95E", "#A6E95F"]
        },
        blue: {
            id:[6,7],
            color:["#51A1FF","#51A1FE"]
        },
        purple: {
            id:[8],
            color:["#D27BFB"]
        },
        // pink: {
        //     id:[9,10],
        //     color:["#ff8dc7","#ff00ff"]
        // },
        gray: {
            id:[11],
            color:["#b3b3b3"]
        }
    }

    static _maxColorCount = -1;
    public static get maxColorCount(){
        if(this._maxColorCount < 0){
            this._maxColorCount = 0;
            for(let i in this.group){
                this._maxColorCount += this.group[i].color.length;
            }
        }

        return this._maxColorCount;
    }
    static get groupColorCount(){
        return Object.keys(this.group).length;
    }

    static getListColor(count){
        const keys = Object.keys(this.group);
        const _gs = [];
        let rCount = Math.min(count, this.maxColorCount);
        let gCount = 0;
        const res : string[] = [];
        while(rCount > 0){
            if(gCount >= this.groupColorCount){
                gCount = 0;
                _gs.splice(0, _gs.length);
            }
            const gs = keys.filter(a=>{
                return _gs.indexOf(a) < 0;
            });
            const g = Math.floor(Math.random()*gs.length);
            const colors = this.group[gs[g]].color.filter(a=>{
                return res.indexOf(a) < 0;
            });
            const c = Math.floor(Math.random()*colors.length);
            res.push(colors.splice(c,1)[0]);
            if(colors.length == 0)
                keys.splice(keys.indexOf(gs[g]), 1);
            gCount++;
            rCount--;
        }
        return res;
    }
    static getListColorId(count) : number[]{
        const keys = Object.keys(this.group);
        const _gs = [];
        let rCount = Math.min(count, this.maxColorCount);
        let gCount = 0;
        const res : number[] = [];
        while(rCount > 0){
            if(gCount >= this.groupColorCount){
                gCount = 0;
                _gs.splice(0, _gs.length);
            }
            const gs = keys.filter(a=>{
                return _gs.indexOf(a) < 0;
            });
            const g = Math.floor(Math.random()*gs.length);
            const ids = this.group[gs[g]].id.filter(a=>{
                return res.indexOf(a) < 0;
            });
            const c = Math.floor(Math.random()*ids.length);
            res.push(ids.splice(c,1)[0]);
            if(ids.length == 0)
                keys.splice(keys.indexOf(gs[g]), 1);
            gCount++;
            rCount--;
        }
        return res;
    }
    static getListBoltColors(count) : number[]{
        const temp = [4, 5, 9];
        return {"3":temp, 
            "4":[1,5,9,6,],
            "5":[1,5,9,6,3], 
            "6":[1,5,9,6,3,4]
        }[count] || temp;
    }
}
```

## File: GameConfig.ts.meta/GameConfig.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "2e930fc4-3eaf-4c29-8faf-2ffa3f222a74",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameLayer.ts/GameLayer.ts
```typescript
import { _decorator, Component, Node, UITransform, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameLayer')
export class GameLayer extends Component {
    start() {
    }

    update(deltaTime: number) {
        
    }

    protected lateUpdate(dt: number): void {
        const cIndex = [];
        const children = this.node.children.concat([]);
        for(let i in children){
            const transform = children[i].getComponent(UITransform);
            cIndex.push(children[i].position.y - transform.height*transform.anchorY);
        }
        const sIndex = cIndex.concat([]);
        sIndex.sort((a,b)=>{return b-a;});
        for(let j = 0; j < sIndex.length; j++){
            const node = children[cIndex.indexOf(sIndex[j])];
            if(node && node.isValid)
                node.setSiblingIndex(+j);
        }
    }
}
```

## File: GameLayer.ts.meta/GameLayer.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "9f02a8be-b874-44ea-aa43-8e205ee82390",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameMap.ts/GameMap.ts
```typescript
import { _decorator, color, Component, error, instantiate, Node, Prefab, tween, UIOpacity } from 'cc';
import GameConfig from './GameConfig';
import { Screw } from './Screw';
import { GameObject } from './GameObject';
import { BoxHolder } from './BoxHolder';
import { FlowUnit } from './flow/FlowUnit';
import AudioManager from './audio/AudioManager';
import { EDITOR } from 'cc/env';
import { VFX } from './miscs/vfx/VFX';
import { Tutorial } from './flow/tutorial/Tutorial';
import { LabelLevel } from './miscs/LabelLevel';
const { ccclass, property } = _decorator;

@ccclass('GameMap')
export class GameMap extends FlowUnit {
    @property
    level = 0;
    @property
    totalBolt = 0;
    @property
    totalLayers = 0;
    @property
    boltColor = 0;
    @property
    boltColorOffset = 0;
    @property
    layerColor:string = "";
    @property
    boltsColor:string = "";
    @property
    get tryRefresh(){
        return false;
    }
    set tryRefresh(v){
        this.refresh();
    }

    @property(Screw)
    guideScrew:Screw = null;

    @property(Prefab)
    winVFX:Prefab = null;
    @property(Prefab)
    loseVFX:Prefab = null;

    start() {
        if(this.refreshOnLoad){
            this.refresh();
        }
        if(Tutorial.i.playOnLoad){            
            LabelLevel.i.level = this.level;
            this.initGuide();
        }
    }
    
    cache(data){
        this.totalBolt  = data.totalSlotCount;
        this.totalLayers  = data.totalLayers;
    }
    get listBolt() : Screw[]{
        let r = [];
        for(let i in this.node.children){
            const layer = this.node.children[i];
            for(let j in layer.children){
                const gObject : Node = layer.children[j];
                // gObject.getComponent(GameObject).body.group = 1 << 3 << (+i + 1);
                r = r.concat(gObject.getComponentsInChildren(Screw));
            }
        }
        return r;
    }

    show(){
        super.show();
        BoxHolder.i.gameMap = this;
        if(!this.refreshOnLoad)
            this.refresh();
        if(!Tutorial.i.playOnLoad)
            this.initGuide();
        LabelLevel.i.level = this.level;
    }

    refresh(){
        const layerColors = this.layerColor ? this.layerColor.split("\t") : GameConfig.getListColor(this.totalLayers);
        const gObjects = [];
        for(let i in this.node.children){
            const layer = this.node.children[i];
            const c = color(layerColors[i].trim());
            for(let j in layer.children){
                const gObject : GameObject = layer.children[j].getComponent(GameObject);
                if(gObject){
                    gObject.img.color = c;
                    gObjects.push(gObject);
                    if(gObject.body)
                        gObject.body.group = 1 << (+i + 1);
                }
            }
        }
        const delay = 0.5/gObjects.length;
        for(let i = 0; i < gObjects.length; i++){
            const opacity = gObjects[i].getComponent(UIOpacity) || gObjects[i].addComponent(UIOpacity);
            if(!this.refreshOnLoad){
                opacity.opacity = 1;
                tween(opacity).delay(delay*i).call(()=>{
                    AudioManager.play("remove");
                }).to(delay, {opacity:255}).start();
            }
        }

        const m = 3 * 2;
        const bolts = this.listBolt;
        this.totalBolt = this.listBolt.length;
        
        const boltColor = this.boltsColor ? this.boltsColor.split("\t") : GameConfig.getListBoltColors(this.boltColor || (Math.min(5, Math.max(3, this.totalBolt/m)) + this.boltColorOffset));
        
        let c = 0;
        let boxData = {};
        let lastBoxColor = -1;
        while(bolts.length > 0){
            if(bolts.length < 3){
                lastBoxColor = +boltColor[c];
            }
            for(let i = 0; i < 3; i++){
                if(bolts.length > 0){
                    const index = Math.floor(Math.random()*bolts.length);
                    const bolt = bolts[index];
                    try{
                        bolt.boltId = +boltColor[c];
                        bolt.node.angle = -bolt.node.parent.angle;
                    }catch(e){
                        error(e);
                    }
                    bolts.splice(index, 1)[0];
                }
            }
            if(!boxData[boltColor[c]])
                boxData[boltColor[c]] = 0;
            boxData[boltColor[c]]++;

            c = (c+1)%boltColor.length;
        }
        if(lastBoxColor != -1)
            boxData[lastBoxColor]--;
        if(this.guideScrew){
            boxData[this.guideScrew.boltId]--;
            if(!boxData[this.guideScrew.boltId]){
                delete boxData[this.guideScrew.boltId];
            }
        }
        BoxHolder.i.init(boxData, lastBoxColor, this.guideScrew?this.guideScrew.boltId:-1);
    }

    initGuide(){
        if(this.guideScrew){
            Tutorial.i.tutorial(this.guideScrew);
        }
    }
    onCollect(){
        Tutorial.i.hide();
    }

    win(){
        if(this.winVFX){
            this.playVFX(this.winVFX, this.nextStep.bind(this));
        }else{
            this.nextStep();
        }
    }
    lose(){
        if(this.loseVFX){
            this.playVFX(this.loseVFX, this.nextStep.bind(this));
        }else{
            this.nextStep();
        }
    }
    playVFX(vfx, callback){
        if(vfx){
            const node = instantiate(vfx);
            this.node.parent.addChild(node);
            this.scheduleOnce(callback, 1.5);
            node.getComponent(VFX).play().then(()=>{
                tween(node.getComponent(UIOpacity)).delay(1).to(0.5, {opacity:0}).call(()=>{
                    node.destroy();
                }).start();
            });
        }
    }

    nextStep(): void {
        AudioManager.play("complete");
        this.manager?.nextStep(true, false);
        this.node.destroy();
    }

    update(deltaTime: number) {
        
    }
}
```

## File: GameMap.ts.meta/GameMap.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "62b8844f-f60d-4667-a493-4590805ff248",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: GameObject.ts/GameObject.ts
```typescript
import { _decorator, Collider2D, Component, PolygonCollider2D, RigidBody2D, Sprite, UITransform, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('GameObject')
export class GameObject extends Component {
    @property(Sprite)
    img:Sprite = null;
    @property(Sprite)
    shadow:Sprite = null;
    @property(Collider2D)
    body:Collider2D = null;

    @property
    get addSprite(){
        return false;
    }
    set addSprite(v){
        if(!this.getComponent(Sprite))
            this.addComponent(Sprite);
        this.getComponent(Sprite).spriteFrame = this.node.getChildByName("img").getComponent(Sprite).spriteFrame;
    }

    @property
    get removeSprite(){
        return false;
    }
    set removeSprite(v){
        if(!this.getComponent(Sprite))
            return
        this.getComponent(Sprite).destroy();
    }

    @property
    get syncImg(){
        return false;
    }
    set syncImg(v){
        if(!this.getComponent(Sprite))
            return
        const frame1 = this.getComponent(Sprite).spriteFrame;
        this.node.getChildByName("img").getComponent(Sprite).spriteFrame = frame1;
        this.node.getChildByName("maskShadow").getComponent(Sprite).spriteFrame = frame1;
        this.node.getChildByName("maskShadow").getChildByName("shadow").getComponent(Sprite).spriteFrame = frame1;
    }


    start() {
        // this.img.node.layer = 1;
    }

    init(layer){
        this.body.group = layer+1;
    }

    update(deltaTime: number) {
        
    }
    protected lateUpdate(dt: number): void {
        const wpos = this.img.getComponent(UITransform).convertToWorldSpaceAR(v3());
        wpos.y -= 15;
        const spos = this.img.getComponent(UITransform).convertToNodeSpaceAR(wpos);
        this.shadow.node.position = spos;
    }
}
```

## File: GameObject.ts.meta/GameObject.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "e1b16188-a32f-4fa8-9fcc-eabc1ff1b36b",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: i18n/LocalizationLabel.ts/LocalizationLabel.ts
```typescript
import { _decorator, Component, Label, Node } from 'cc';
import StringManager from './StringManager';
const { ccclass, property } = _decorator;

@ccclass('LocalizationLabel')
export class LocalizationLabel extends Component {
    @property
    _i18n:string = "";
    @property
    get i18n():string{
        return this._i18n;
    }
    set i18n(v){
        this._i18n = v;
        this.refresh();
    }

    start() {

    }

    protected onEnable(): void {
        this.refresh();
    }

    refresh(){
        this.getComponent(Label).string = StringManager.i.getString(this.i18n);
    }

    update(deltaTime: number) {
        
    }
}
```

## File: i18n/LocalizationLabel.ts.meta/LocalizationLabel.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "4b5aa046-9903-4de7-9333-a82a510c33a0",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: i18n/StringManager.ts/StringManager.ts
```typescript
import { sys } from "cc";

const config = {
    en:{
        MAP_INTRO_V1: "Goal",
        MAP_INTRO_V1_2: "Find Screws matches Box Color",
        NORMAL_INTRO: "Match Screws and Decorate!",
        NORMAL_OUTRO: "Screws Match and Decoration Game",
        PLAY_NOW: "Play Now",
        DOWNLOAD_NOW: "Download Now!!",
        BTN_DOWNLOAD: "Download Now",
        LET_TRY: "Let's Try it",
        DECOR_INTRO: "Let's decorate this room",
        TAP_TO_CONTINUE: "Tap To Continue",
    },
    de:{
        MAP_INTRO_V1: "Ziel",
        MAP_INTRO_V1_2: "Finden Sie Schrauben, die zur\nKastenfarbe passen",
        NORMAL_INTRO: "Schrauben zusammenfügen und dekorieren!",
        NORMAL_OUTRO: "Schrauben-Verbindungs- und Dekorationsspiel",
        PLAY_NOW: "Jetzt Spielen",
        DOWNLOAD_NOW: "Jetzt Herunterladen!!",
        BTN_DOWNLOAD: "Jetzt Herunterladen",
        LET_TRY: "Lass es uns versuchen",
        DECOR_INTRO: "Lass es uns versuchen",
        TAP_TO_CONTINUE: "Tippe, um fortzufahren",
    },
    es:{
        MAP_INTRO_V1: "Objetivo",
        MAP_INTRO_V1_2: "Encuentra los tornillos que\ncoincidan con el color de la caja",
        NORMAL_INTRO: "¡Combina los tornillos y decora!",
        NORMAL_OUTRO: "Juego de combinación de tornillos y decoración",
        PLAY_NOW: "Jugar Ahora",
        DOWNLOAD_NOW: "¡Descargar Ahora!!",
        BTN_DOWNLOAD: "¡Descargar Ahora",
        LET_TRY: "Intentémoslo",
        DECOR_INTRO: "Vamos a decorar esta habitación",
        TAP_TO_CONTINUE: "Toca para continuar",
    },
    pt:{
        MAP_INTRO_V1: "Objetivo",
        MAP_INTRO_V1_2: "Encontre os parafusos que\ncombinem com a cor da caixa",
        NORMAL_INTRO: "Combine os parafusos e decore!",
        NORMAL_OUTRO: "Jogo de combinação de parafusos e decoração",
        PLAY_NOW: "Jogar Agora",
        DOWNLOAD_NOW: "Baixar Agora!!",
        BTN_DOWNLOAD: "Baixar Agora",
        LET_TRY: "Vamos tentar",
        DECOR_INTRO: "Vamos decorar este quarto",
        TAP_TO_CONTINUE: "Toque para continuar",
    },
    kr:{
        MAP_INTRO_V1: "목표",
        MAP_INTRO_V1_2: "상자 색상과 일치하는\n나사를 찾으세요",
        NORMAL_INTRO: "나사를 맞추고 장식하세요!",
        NORMAL_OUTRO: "나사 맞추기 및 장식 게임",
        PLAY_NOW: "지금 플레이하기",
        DOWNLOAD_NOW: "지금 다운로드하기!!",
        BTN_DOWNLOAD: "지금 다운로드하기",
        LET_TRY: "해보자",
        DECOR_INTRO: "이 방을 꾸며봅시다",
        TAP_TO_CONTINUE: "탭하여 계속하기",
    }
};

export default class StringManager{
    private static _i : StringManager = null;
    public static get i():StringManager{
        if(this._i == null)
            this._i = new StringManager();
        return this._i;
    }

    // data = JSON.parse(JSON.stringify(config[Intl.DateTimeFormat().resolvedOptions().locale] || config.en));
    data = JSON.parse(JSON.stringify(config[sys.language] || config.en));
    
    getString(id) : string{
        return this.data[id] || id;
    }
}
```

## File: i18n/StringManager.ts.meta/StringManager.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "8d2a6bda-6743-4837-bb0c-3e5ecd607803",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: i18n.meta/i18n.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "b3cb9228-679d-45e0-ab2c-d0565231faa4",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: MapGenerator.ts/MapGenerator.ts
```typescript
import { _decorator, Component, error, instantiate, js, JsonAsset, log, Node, Prefab, v3, Widget } from 'cc';
import { GameMap } from './GameMap';
import { GameObject } from './GameObject';
import { GameLayer } from './GameLayer';
import { Screw } from './Screw';
const { ccclass, property } = _decorator;

@ccclass('MapGenerator')
export class MapGenerator extends Component {
    @property([JsonAsset])
    json:JsonAsset[] = [];

    @property(Node)
    itemHolder:Node = null;
    @property(Node)
    noItem:Node = null;

    _generate = false;
    @property
    get generate(){
        return this._generate;
    }
    set generate(v){
        this.generateMap();
    }
    _info = false;
    @property
    get info(){
        return this._info;
    }
    set info(v){
        this.showInfo();
    }


    start() {
        this.generateMap();
    }

    update(deltaTime: number) {
        
    }

    getItem(id){
        for(let i in this.itemHolder.children){
            if(this.itemHolder.children[i].name == id){
                const node = instantiate(this.itemHolder.children[i]);
                return node;
            }
        }
        const node = instantiate(this.noItem);
        node.active = true;
        return node;
    }

    showInfo(){
        try{
            const objectId = [];
            for(let x = 0; x < this.json.length; x++){
                log(this.json[x].json)
                const layers = this.json[x].json.layeredObjectSpawns;
                for(let i in layers){
                    let layer = layers[i];
                    for(let j in layer.objectSpawnList){
                        let data = layer.objectSpawnList[j];
                        if(objectId.indexOf(data.Id) < 0){
                            objectId.push(data.Id);
                        }
                    }
                }
            }
            objectId.sort((a,b)=>{
                return (+a)-(+b);
            });
            log(JSON.stringify(objectId));
        }catch(e){
            error(e)
        }
    }

    generateMap(){        
        for(let x = 0; x < this.json.length; x++){
            const layers = this.json[x].json.layeredObjectSpawns;
            const mapNode = new Node();
            mapNode.name = this.json[x].name;
            const widget = mapNode.addComponent(Widget);
            widget.isAlignTop = true;
            widget.isAlignBottom = true;
            widget.isAlignLeft = true;
            widget.isAlignRight = true;
            widget.top = 0;
            widget.bottom = 0;
            widget.left = 0;
            widget.right = 0;
            const mapItem = mapNode.addComponent(GameMap);
            mapItem.cache(this.json[x].json);
            this.node.addChild(mapNode);
            for(let i in layers){
                let layer = layers[i];
                const layerNode = new Node();
                layerNode.layer = 1 << (+i);
                layerNode.name = i;
                layerNode.addComponent(GameLayer);
                mapNode.addChild(layerNode);
                for(let j in layer.objectSpawnList){
                    let data = layer.objectSpawnList[j];
                    let node = this.getItem(data.Id);
                    // node.name = data.Id;
                    node.active = true;
                    node.position = v3(data.Position.x*100, data.Position.y*100);
                    node.angle = data.Angle;
                    layerNode.addChild(node);
                    for(let b in node.children){
                        if(node.children[b].name.startsWith("screw")){
                            node.children[b].angle = -data.Angle;
                        }
                    }
                }
            }
        }
    }
}
```

## File: MapGenerator.ts.meta/MapGenerator.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "c4e7399b-1d3a-49d7-bb23-f033b184a3ef",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/AnimationCallback.ts/AnimationCallback.ts
```typescript
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AnimationCallback')
export class AnimationCallback extends Component {
    start() {

    }

    emit(event){
        this.node.emit(event);
    }

    update(deltaTime: number) {
        
    }
}
```

## File: miscs/AnimationCallback.ts.meta/AnimationCallback.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "82e2dd40-64cb-4c7d-8de1-80bde98888c7",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/AutoFill.ts/AutoFill.ts
```typescript
import { _decorator, Component, Node, UITransform, v3, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('AutoFillSprite')
export class AutoFillSprite extends Component {
    refresh(){
        const transform = this.getComponent(UITransform);
        if(!transform)
            return;
        const transform2 = this.node.parent.getComponent(UITransform);
        const scaleX = transform2.width/transform.width;
        const scaleY = transform2.height/transform.height;
        const scale = Math.max(scaleX, scaleY);
        this.node.scale = v3(scale, scale, 1);
    }

    update(deltaTime: number) {
        
    }

    protected lateUpdate(dt: number): void {
        this.refresh();
    }
}
```

## File: miscs/AutoFill.ts.meta/AutoFill.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "46ddda86-2fa8-4387-8aaa-35a5d0e0089e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/HandRing.ts/HandRing.ts
```typescript
import { _decorator, Component, Node, Sprite, tween, v2, Vec2 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('HandRing')
export class HandRing extends Component {
    @property(Sprite)
    sprite:Sprite = null;

    _radius:Vec2 = v2(0,0);
    get radius(){
        return this._radius;
    }
    set radius(v){
        this._radius = v;
        this.sprite.customMaterial.setProperty("radius", [this.radius.x, this.radius.y]);
    }
    
    @property
    delay1:number = 0;
    @property
    delay2:number = 0;
    @property
    time:number = 1;
    @property(Vec2)
    from:Vec2 = v2(0.5, 0.8);
    @property(Vec2)
    to:Vec2 = v2(1,1);

    start() {
        tween(this as HandRing).repeatForever(
            tween().delay(this.delay1).call(()=>{
                this.radius = this.from;
            }).to(this.time, {radius:this.to}).delay(this.delay2)
        ).start();
    }
}
```

## File: miscs/HandRing.ts.meta/HandRing.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "52e4deda-995c-4753-9a6b-30142358b56e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/LabelAnim.ts/LabelAnim.ts
```typescript
import { _decorator, color, Color, Component, instantiate, Label, Layout, Node, tween, UIOpacity, UITransform, v3 } from 'cc';
import StringManager from '../i18n/StringManager';
const { ccclass, property } = _decorator;

@ccclass('LabelAnim')
export class LabelAnim extends Component {
    @property
    _stringId:string = "";
    @property
    get stringId():string{
        return this._stringId;
    }
    set stringId(v){
        this._stringId = v;
        this.refresh();    
    }

    @property
    playOnLoad = false;

    @property
    initTime:number = 1;
    @property
    idleTime:number = 1;
    @property
    delayTime:number = 1;

    @property(Node)
    temp: Node = null;

    @property([Color])
    colors: Color[] = [];

    @property(Node)
    holder: Node = null;

    @property([Label])
    labels: Label[] = [];

    onLoad(){

    }
    refresh(){
        if(this.holder){
            this.holder.removeAllChildren();
        }
        const content : string = StringManager.i.getString(this.stringId);
        const s1 = content.split('\n');
        for(let i = 0; i < s1.length; i++){
            const s = s1[i].split(' ');
            const row = new Node().addComponent(Layout);
            row.node.name = "row"+i;
            row.type = Layout.Type.HORIZONTAL;
            row.resizeMode = Layout.ResizeMode.CONTAINER;
            row.spacingX = 15;
            row.getComponent(UITransform).height = 65;
            row.affectedByScale = true;

            for(let j = 0; j < s.length; j++){
                const node = instantiate(this.temp);
                row.node.addChild(node);
                const label = node.getComponent(Label);
                label.string = s[j];
                label.color = this.colors[(j+i)%this.colors.length];
    
                this.labels.push(label);
            }
            (this.holder||this.node).addChild(row.node);
        }
    }
    
    start() {
        if(this.playOnLoad)
            this.play();
    }

    play(){
        this.refresh();
        const t1 = this.initTime/this.labels.length;
        for(let i = 0; i < this.labels.length; i++){
            this.labels[i].node.active = true;
            const opacity = this.labels[i].addComponent(UIOpacity);
            opacity.opacity = 0;
            tween(opacity).delay(t1 * i).to(t1, {opacity:255}).delay(i*this.idleTime/2).start();
        }
    }
    play2(delayIdle=0){
        this.refresh();
        const t1 = this.initTime/this.labels.length;
        for(let i = 0; i < this.labels.length; i++){
            this.labels[i].node.active = true;
            const opacity = this.labels[i].addComponent(UIOpacity);
            opacity.opacity = 0;
            tween(opacity).delay(t1 * i).to(t1, {opacity:255}).call(()=>{
                if(i == this.labels.length-1){
                    this.node.emit("full");
                }
            }).delay(delayIdle+i*this.idleTime/2).call(()=>{
                tween(this.labels[i].node).delay(this.delayTime-t1 * i)
                    .to(this.idleTime/2, {scale:v3(1.2, 1.2, 1)}).to(this.idleTime/2, {scale:v3(1,1,1)}).start()
            }).delay(i*this.idleTime/2).call(()=>{
                if(i == this.labels.length-1){
                    this.node.emit("endIdle");
                }
            }).start();
        }
    }

    update(deltaTime: number) {
        
    }
}
```

## File: miscs/LabelAnim.ts.meta/LabelAnim.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "c28cd292-9ea3-405f-9180-f1f0d5dc0ec8",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/LabelLevel.ts/LabelLevel.ts
```typescript
import { _decorator, Component, Label, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('LabelLevel')
export class LabelLevel extends Component {
    public static i : LabelLevel = null;

    @property
    _level = 0;
    @property
    get level(){
        return this._level;
    }
    set level(v){
        this._level = v;
        this.refreshLv();
    }
    refreshLv(){
        for(let i = 0; i < this.lbLevel.length; i++){
            this.lbLevel[i].string = this.level <= 0 ? "" : "LEVEL " + this.level;
        }
    }
    @property([Label])
    lbLevel:Label[] = [];

    start() {
        LabelLevel.i = this;
    }

    update(deltaTime: number) {
        
    }
}
```

## File: miscs/LabelLevel.ts.meta/LabelLevel.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "fdfeac0e-19de-4132-a027-1580aa572170",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/vfx/Remarkable.ts/Remarkable.ts
```typescript
import { _decorator, Component, Node, ParticleSystem2D, Sprite, SpriteFrame, tween, UIOpacity, v2, v3 } from 'cc';
import { VFX } from './VFX';
const { ccclass, property } = _decorator;

@ccclass('Remarkable')
export class Remarkable extends VFX {
    play(){
        return new Promise(resolve=>{
            const bg = this.node.getChildByName("bg").getComponent(UIOpacity);
            bg.opacity = 0;
            tween(bg).to(0.5, {opacity:255}).call(()=>{
                this.node.getChildByName("particle1").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle2").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle3").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle4").getComponent(ParticleSystem2D).resetSystem();
            }).start();
    
            const txt = this.node.getChildByName("txt");
            for(let i = 0; i < txt.children.length; i++){
                const char = txt.children[i];
                char.scale = v3(0,0,0);
                tween(char).delay(0.5+0.1*i).to(0.5, {scale:v3(1.4, 1.4, 1)}).to(0.2, {scale:v3(1,1,1)}).start();
            }
    
            const glow = this.node.getChildByName("glow").getComponent(UIOpacity);
            glow.opacity = 0;
            const t= txt.children.length*0.1+1.1;
            glow.node.position = txt.position;
            tween(glow).delay(t).to(0.3, {opacity:255}).delay(0.3).to(0.3, {opacity:0}).start();
            tween(glow.node).delay(t+0.15).by(0.4, {position:v3(15, 10)}).by(0.4, {position:v3(-10,-10)}).call(resolve).start();
        })
    }

    start(){
        this.play();
    }
}
```

## File: miscs/vfx/Remarkable.ts.meta/Remarkable.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "400ea426-d2a9-4e68-bc31-8a4a624db32a",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/vfx/VFX.ts/VFX.ts
```typescript
import { _decorator, Component, Node, ParticleSystem2D, Sprite, SpriteFrame, tween, UIOpacity, v2, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('VFX')
export class VFX extends Component {
    play():Promise<unknown>{
        return new Promise(resolve=>{
            resolve(null);
        })
    }
}
```

## File: miscs/vfx/VFX.ts.meta/VFX.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "d36212e8-c864-4c3d-bf60-b6e450a8dbdc",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs/vfx.meta/vfx.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "463339db-fd55-41f6-b298-0e18bf0c64cb",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: miscs.meta/miscs.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "5da94a24-6929-4b75-b421-0a00bea39f9e",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: room/RoomDecor.ts/RoomDecor.ts
```typescript
import { _decorator, Animation, color, Component, instantiate, JsonAsset, log, Material, Node, ParticleSystem2D, Sprite, Tween, tween, UITransform, v2, v3, view } from 'cc';
import { RoomLayerMaterial } from './RoomLayerMaterial';
import { RoomLayer } from './RoomLayer';
import { FlowUnit } from '../flow/FlowUnit';
import AudioManager from '../audio/AudioManager';
const { ccclass, property } = _decorator;

@ccclass('RoomDecor')
export class RoomDecor extends Component {
    @property(Material) material: Material = null;
    @property(JsonAsset) oldConfig:JsonAsset = null;
    @property(JsonAsset) config:JsonAsset = null;
    @property 
    get generate(){return false;}
    set generate(v){this._generate();}

    @property(FlowUnit)
    flowUnit:FlowUnit = null;
    @property(Node)
    selectNode:Node = null;
    @property(Node)
    introTxt:Node = null;
    @property(Node)
    introBtn:Node = null;
    @property(Node)
    guide:Node = null;
    @property(Node)
    btnDownload:Node = null;
    @property(ParticleSystem2D)
    particle1:ParticleSystem2D = null;
    @property(ParticleSystem2D)
    particle2:ParticleSystem2D = null;

    decorated = [];

    _generate(){
        for(let i = this.config.json.layeredRoomSpawns.length-1; i >= 0; i--){
            const json = this.config.json.layeredRoomSpawns[i];
            // if(json.id == 5)
            this.generateLayer(json);
        }
        for(let i in this.oldConfig.json.layeredRoomSpawns){
            const json = this.oldConfig.json.layeredRoomSpawns[i];
            // if(json.Id == 5)
            this.generateButton(json);
        }
    }
    onLoad(){
        this._generate();
        this.refreshScale();
    }
    protected lateUpdate(dt: number): void {
        this.refreshScale();
    }
    refreshScale(){

        const design = view.getDesignResolutionSize();
        const visible = view.getVisibleSize();
        let scale = visible.width*design.height/design.width/visible.height;
        scale = Math.max(scale, 1/scale);
        this.node.scale = v3(scale, scale, scale);
    }
    start() {
        this.showIntro();
    }
    checkEndFlow(){
        if(this.decorated.length == this.node.getChildByName("button").children.length){
            this.flowUnit.nextStep();
        }
    }
    showIntro(){
        this.introTxt.active = true;
        this.introBtn.active = true;
        tween(this.introTxt).repeatForever(tween().to(1, {scale:v3(1.1,1.1,1)}, {easing:'sineInOut'}).to(1, {scale:v3(1,1,1)}, {easing:'sineInOut'})).start();
    }
    hideIntro(){
        Tween.stopAllByTarget(this.introTxt);
        this.introTxt.active = false;
        this.introBtn.active = false;
        this.showEditButton();
        this.showGuide(1);
    }
    hideSelect(){
        tween(this.selectNode).to(0.3, {position:v3(0, -300)}, {easing:'backIn'}).start();
    }
    showSelect(data){
        const layer = this.node.getChildByName("layer").getChildByName(this.config.json.layeredRoomSpawns.find(a=>{return a.id == data.Id}).objectNameDefault);
        tween(this.selectNode).to(0.3, {position:v3(0, 0)}, {easing:'backOut'}).start();
        const btns = this.selectNode.getChildByName("color").children;
        const datas = this.config.json.layeredRoomSpawns.find(a=>{return a.id == data.Id});
        const resetBtnExcept = (index)=>{
            btns.forEach((a,i)=>{index != i ? a.scale = v3(1,1,1) : null})
        }
        resetBtnExcept(-1);
        for(let i in btns){
            const node = btns[i];
            node.off("click");
            node.on("click", ()=>{
                AudioManager.play("remove");
                resetBtnExcept(i);
                node.scale = v3(1.05, 1.05, 1);
                this.updateLayer(layer, +i);
                this.selectNode.getChildByName("ok").active = true;
            })
            const c = datas.objectSpawnList[i].colorTint;
            node.getComponent(Sprite).color = color(c.r*255, c.g*255, c.b*255, c.a*255);
        }
        
        this.selectNode.getChildByName("ok").active = false;
        this.selectNode.getChildByName("ok").off("click");
        this.selectNode.getChildByName("cancel").off("click");

        this.playParticle(layer, 1);

        this.selectNode.getChildByName("ok").on("click", ()=>{
            AudioManager.play("remove");
            this.decorated.push(this.node.getChildByName("button").getChildByName("btn_"+data.Id));
            this.hideSelect();
            this.showEditButton();
            this.showGuide(5);
            this.checkEndFlow();
            
            this.playParticle(layer, 2);

            this.particle1.stopSystem();

            AudioManager.play("popup");
        });
        this.selectNode.getChildByName("cancel").on("click", ()=>{
            AudioManager.play("remove");
            this.updateLayer(layer, -1);
            this.hideSelect();
            this.showEditButton();
            this.showGuide(5);
        });
    }
    playParticle(target, id){
        this['particle'+id].node.position = target.position;
        const transform = target.getComponent(UITransform);
        this['particle'+id].posVar = v2(transform.width/2, transform.height/2);
        this['particle'+id].resetSystem();
    }
    showEditButton(){
        this.btnDownload.active = true;
        const btns = this.node.getChildByName("button").children;
        for(let i in btns){
            if(this.decorated.indexOf(btns[i]) != -1)
                continue;
            btns[i].active = true;
            btns[i].scale = v3(0,0,0);
            Tween.stopAllByTarget(btns[i]);
            tween(btns[i]).delay(0.05*(+i)).call(()=>{
                AudioManager.play("remove");
            }).to(0.1, {scale:v3(1,1,1)}, {easing:'backOut'}).start();
        }
    }
    hideEditButton(){
        this.btnDownload.active = false;
        const btns = this.node.getChildByName("button").children;
        for(let i in btns){
            if(this.decorated.indexOf(btns[i]) != -1)
                continue;
            btns[i].active = true;
            btns[i].scale = v3(1,1,1);
            Tween.stopAllByTarget(btns[i]);
            tween(btns[i]).to(0.1, {scale:v3(0,0,0)}, {easing:'backIn'}).start();
        }
    }
    showGuide(delay){
        this.guide.getComponent(Component).scheduleOnce(()=>{
            const btns = this.node.getChildByName("button").children.filter((node)=>{return this.decorated.indexOf(node) == -1});
            this.guide.position = btns[Math.floor(Math.random()*btns.length)].position;
            this.guide.scale = v3(0,0,0);
            Tween.stopAllByTarget(this.guide);
            tween(this.guide).to(0.1, {scale:v3(0.5,0.5,0.5)}).call(()=>{this.guide.getComponent(Animation).play()}).start();
        }, delay)
    }
    hideGuide(){
        this.guide.getComponent(Component).unscheduleAllCallbacks();
        Tween.stopAllByTarget(this.guide);
        tween(this.guide).to(0.1, {scale:v3()}).start();
    }
    
    generateLayer(data){
        const res = this.node.getChildByName("resources");
        const layer = res.getChildByName(data.objectNameDefault);
        if(layer){
            const node = instantiate(layer);
            const pos = v3(data.objectSpawnList[0].position.x*100, data.objectSpawnList[0].position.y*100);
            node.position = pos;
            node.addComponent(RoomLayerMaterial).init(this);
            node.addComponent(RoomLayerMaterial).sprite = node.getComponent(Sprite);
            this.node.getChildByName("layer").addChild(node);
        }
    }
    generateButton(data){
        const btn = this.node.getChildByName("btn");
        const node = instantiate(btn);
        node.name = "btn_"+data.Id
        // node.active = true;
        node.on('click', ()=>{
            AudioManager.play("remove");
            // const layers = this.node.getChildByName("layer").children;
            // this.updateLayer(layers[layers.length-data.Id], Math.floor(Math.random()*4));
            this.hideEditButton();
            this.showSelect(data);
            this.hideGuide();
        }, this)
        node.position = v3(data.buttonPosition.x*100, data.buttonPosition.y*100);
        this.node.getChildByName("button").addChild(node);
    }
    updateLayer(node:Node, id:number){
        const data = this.config.json.layeredRoomSpawns.find(a=>{
            return a.objectNameDefault == node.name;
        });
        if(data){
            if(id < 0){
                const frame = this.node.getChildByName("resources").getChildByName(data.objectNameDefault).getComponent(Sprite).spriteFrame;
                node.getComponent(RoomLayer).reset(frame);
            }else{
                const frame = this.node.getChildByName("resources").getChildByName(data.objectSpawnList[id].objectName).getComponent(Sprite).spriteFrame;
                node.getComponent(RoomLayer).updateLayer(frame, data.objectSpawnList[id]);
            }
        }
    }

    update(deltaTime: number) {
        
    }
}
```

## File: room/RoomDecor.ts.meta/RoomDecor.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "9f748f9c-e903-4350-a2f5-2d127257fb5b",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: room/RoomLayer.ts/RoomLayer.ts
```typescript
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('RoomLayer')
export class RoomLayer extends Component {
    init(room){

    }
    reset(frame){

    }
    refresh(){
        
    }
    
    updateLayer(frame, data){
        
    }
}
```

## File: room/RoomLayer.ts.meta/RoomLayer.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "b19cc7c8-32e0-4005-91a1-28a0b5046fe3",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: room/RoomLayerMaterial.ts/RoomLayerMaterial.ts
```typescript
import { _decorator, color, instantiate, ParticleSystem2D, Sprite, tween, UIOpacity, UITransform, v2 } from 'cc';
import { RoomLayer } from './RoomLayer';
const { ccclass, property } = _decorator;

@ccclass('RoomLayerMaterial')
export class RoomLayerMaterial extends RoomLayer {    
    @property 
    _mode = false;
    @property
    get mode(){return this._mode;}
    set mode(v){
        this._mode = v;
        this.refresh();
    }
    
    @property
    _brightness = 1;
    @property
    _contrast = 1;
    @property
    _saturation = 1;
    @property
    _hue = 0;
    @property
    _light = 1.5;
    
    @property({min:0, max:2, slide:true, step:0.01})
    get brightness(){return this._brightness;}
    set brightness(v){
        this._brightness = v;
        this.refresh();
    }
    @property({min:0, max:2, slide:true, step:0.01})
    get contrast(){return this._contrast;}
    set contrast(v){
        this._contrast = v;
        this.refresh();
    }
    @property({min:0, max:2, slide:true, step:0.01})
    get saturation(){return this._saturation;}
    set saturation(v){
        this._saturation = v;
        this.refresh();
    }
    @property({min:-2, max:2, slide:true, step:0.01})
    get hue(){return this._hue;}
    set hue(v){
        this._hue = v;
        this.refresh();
    }
    @property({min:0, max:2, slide:true, step:0.01})
    get light(){return this._light;}
    set light(v){
        this._light = v;
        this.refresh();
    }
    @property(Sprite)
    sprite : Sprite = null;
    room : any = null;

    init(room){
        this.room = room;
    }
    reset(frame){
        this.sprite.spriteFrame = frame;
        this.sprite.color = color(255, 255, 255, 255);
        
        this._mode = false;
        this._brightness = 1;
        this._contrast = 1;
        this._saturation = 0;
        this._hue = 0;
        this.refresh();
    }
    onLoad(){
        if(!this.sprite)
            this.sprite = this.getComponent(Sprite);
    }
    start() {
    }
    updateLayer(frame, data){
        const over = instantiate(this.node);
        this.node.parent.addChild(over);
        // over.position = v3(over.position.x, over.position.y - 100);
        over.setSiblingIndex(this.node.getSiblingIndex()+1);
        const layer = over.getComponent(RoomLayerMaterial);
        layer.init(this.room);
        layer.refresh();

        // this.room.particle1.node.position = this.node.position;
        // const transform = layer.getComponent(UITransform);
        // this.room.particle1.posVar = v2(transform.width/2, transform.height/2);
        // this.room.particle1.resetSystem();
        this.playParticle(1);

        tween(over.addComponent(UIOpacity)).to(0.3, {opacity:0}).call(()=>{
            over.destroy();
        }).start();

        this.sprite.spriteFrame = frame;
        this.sprite.color = color(data.colorTint.r*255, data.colorTint.g*255, data.colorTint.b*255, data.colorTint.a*255);
        this._mode = data.colorMode == 1;
        this._brightness = data.brightness;
        this._contrast = data.contrast;
        this._saturation = data.saturation;
        this._hue = data.hue;
        this.refresh();
    }
    playParticle(id){
        const node = instantiate(this.room["particle"+id].node);
        node.position = this.node.position;
        const particle : ParticleSystem2D = node.getComponent(ParticleSystem2D);
        particle.autoRemoveOnFinish = true;
        this.node.parent.addChild(node);
        const transform = this.getComponent(UITransform);
        particle.posVar = v2(transform.width/2, transform.height/2);
        particle.resetSystem();
    }
    refresh(){
        this.sprite.setMaterialInstance(this.room.material, 0);
        this.sprite.materials[0].setProperty("mode", this.mode?1.0:0.0);
        this.sprite.materials[0].setProperty("hue", this.hue/10);
        // this.sprite.materials[0].setProperty("brightness", Math.min(this.light, this.brightness));
        this.sprite.materials[0].setProperty("brightness", 1+(this.brightness-1)/2);
        this.sprite.materials[0].setProperty("contrast", this.contrast);
        this.sprite.materials[0].setProperty("saturation", this.saturation);
        // this.sprite.materials[0].setProperty("grayscale", Math.max(0, (this.brightness-this.light)/2));
    }

    lateUpdate(deltaTime: number) {
        // this.refresh();
    }
}
```

## File: room/RoomLayerMaterial.ts.meta/RoomLayerMaterial.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "c4b3e585-6842-4f14-b9cd-52e56780eafd",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: room/RoomLayerSprite.ts/RoomLayerSprite.ts
```typescript
import { _decorator, Component, Node, Sprite, SpriteFrame } from 'cc';
import { RoomLayer } from './RoomLayer';
const { ccclass, property } = _decorator;

@ccclass('RoomLayerSprite')
export class RoomLayerSprite extends RoomLayer {
    @property(Sprite) sprite:Sprite = null;
    @property([SpriteFrame]) frames: SpriteFrame[] = [];
}
```

## File: room/RoomLayerSprite.ts.meta/RoomLayerSprite.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "6ddbd2a8-9c37-44dd-adde-d5d12bff7b09",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: room.meta/room.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "f5fddc08-4c7e-4e83-aa6b-f934c63dff5c",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: Screenshake.ts/Screenshake.ts
```typescript
import { _decorator, Component, Node, v3 } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('Screenshake')
export class Screenshake extends Component {
    public static instance :Screenshake = null;
    start() {
        Screenshake.instance = this;
    }

    range = 5;
    duration = 1;
    time = 0;
    shake(){
        // this.range = 5;
        // this.time = this.duration;
    }
    update(deltaTime: number) {
        // if(this.time > 0){
        //     this.time-=deltaTime;
        //     if(this.time < 0)
        //         this.time = 0;
        //     if(this.time == 0){
        //         this.node.position = v3(0,0,0);
        //     }else{
        //         this.range *= (this.time)/this.duration;
        //         const x = Math.random()*2*this.range - this.range;
        //         const y = Math.random()*2*this.range - this.range;

        //         this.node.position = v3(x,y);
        //     }
        // }
    }
}
```

## File: Screenshake.ts.meta/Screenshake.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "d3794fd7-8eee-4ff4-912d-b927b44e3298",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: Screw.ts/Screw.ts
```typescript
import { _decorator, Button, Collider2D, Component, Joint2D, log, Node, Sprite, Tween, tween, UIOpacity, UITransform, v2, v3, Vec2, Vec3 } from 'cc';
import { StaticResources } from './StaticResources';
import { Fake3D } from './Fake3D';
import { BoxHolder } from './BoxHolder';
import { TopLayer } from './TopLayer';
import AudioManager from './audio/AudioManager';
import { GameObject } from './GameObject';
import { GameCamera } from './GameCamera';
import { Screenshake } from './Screenshake';
const { ccclass, property } = _decorator;

@ccclass('Screw')
export class Screw extends Component {
    _boltId = 0;
    @property({step:1, max:11, min:0, slide:true})
    get boltId(){
        return this._boltId;
    }
    set boltId(v){
        this._boltId = v;
        this.thread.spriteFrame = StaticResources.i.screwThread[this.boltId];
        this.bolt.spriteFrame = StaticResources.i.boltCommand[this.boltId];
    }

    @property(Sprite)
    bolt: Sprite = null;
    @property(Sprite)
    thread: Sprite = null;
    @property(Joint2D)
    joint:Joint2D = null;
    
    _mapJoint = false;
    @property
    get mapJoint(){
        return this._mapJoint;
    }
    set mapJoint(v){
        this.joint.connectedAnchor = v2(this.node.position.x, this.node.position.y);
    }

    start() {
        // this.bolt.node.layer = 2;
    }

    collected = false;

    update(deltaTime: number) {
        
    }
    protected lateUpdate(dt: number): void {
        const fake3D = this.getComponent(Fake3D);
        if(fake3D){
            const max = 62;
            const height = Math.max(0, Math.min(max, fake3D.z*0.15));
            this.thread.getComponent(UITransform).height = height;
        }
    }

    fly(posInHolder, holder){
        return new Promise(resolve=>{
            AudioManager.play("remove");
            const top = TopLayer.i.node.getComponent(UITransform);
            this.node.position = top.convertToNodeSpaceAR(this.getComponent(UITransform).convertToWorldSpaceAR(v3()));
            this.node.setParent(top.node);
            this.node.angle = 0;
    
            const posInWorld = holder.getComponent(UITransform).convertToWorldSpaceAR(posInHolder);
            let currentPosInWorld = this.node.parent.getComponent(UITransform).convertToWorldSpaceAR(this.node.position);
            let offset = posInWorld.subtract(currentPosInWorld);
            const distance = Math.sqrt(offset.x*offset.x + offset.y*offset.y);
            const velocity = 2500;
            const time = distance/velocity;
            const t1 = time * 0.7;
            const t2 = 0.2;
            const fake3D = this.getComponent(Fake3D);
            if(fake3D){
                Tween.stopAllByTarget(fake3D);
                tween(fake3D).to(time, {z:1000})
                // .call(this.leaveObject.bind(this))
                .to(t2, {z:0}, {easing:"sineIn"}).call(resolve).start();
            }
            Tween.stopAllByTarget(this.node);
            tween(this.node).by(time, {position:offset}).delay(t2).call(()=>{
                this.node.position = posInHolder;
                this.node.setParent(holder);
                this.collected= true;
            }).start();
            Tween.stopAllByTarget(this.bolt.node);
            tween(this.bolt.node).by(t1, {angle:3600*t1}).delay(time-t1).by(t2, {angle:-3600*t2}).call(()=>{
                this.bolt.node.angle = 0;
            }).start();
        })
    }
    leaveObject(){
        this.joint.enabled = false;
    }
    get blocked(){

        return true;
    }
    onClick(){
        this.checkBlock().then(block=>{
            if(!block){
                const box = BoxHolder.i.getBox(this.boltId);
                if(!box || !box.ready)
                    return;
                const slot = box.getSlot();
                if(!slot)
                    return;
                this.leaveObject();
                Screenshake.instance.shake();
                this.getComponent(Button).interactable = false;
                box.putScrew(this);
                slot["occupied"] = true;
                this.fly(slot.position, box.holder).then(()=>{
                    BoxHolder.i.checkFullBox(box);
                    BoxHolder.i.onCollectBolt(box, this);
                });
            }
        });
    }

    tutorial(){
        return new Promise(resolve=>{
            const postion = this.node.parent.getComponent(UITransform).convertToNodeSpaceAR(
                BoxHolder.i.queue[0].slots[0].getComponent(UITransform).convertToWorldSpaceAR(v3()));
            this.fly(postion, this.node.parent).then(()=>{
                resolve(null)
            });
        });
    }

    checkBlock(){
        const layer = this.node.parent.parent;//object->layer
        const layerIndex = +layer.name;
        const imgs = [];
        for(let i = layerIndex+1; i < layer.parent.children.length; i++){
            for(let j in layer.parent.children[i].children){
                const img = layer.parent.children[i].children[j].getComponent(GameObject)?.img ?? null;
                if(img)
                    imgs.push(img);
            }
        }
        return GameCamera.i.checkBoltBlock(this.bolt, imgs);
    }
}
```

## File: Screw.ts.meta/Screw.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "b561aefb-7a94-41e8-ab78-688aefd0c593",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: ShapeGenerator.ts/ShapeGenerator.ts
```typescript
import { _decorator, assetManager, Component, instantiate, js, JsonAsset, log, Node, PolygonCollider2D, Prefab, Sprite, v2, v3 } from 'cc';
import { EDITOR } from 'cc/env';
import { Screw } from './Screw';
const { ccclass, property } = _decorator;

@ccclass('ShapeGenerator')
export class ShapeGenerator extends Component {
    @property(Prefab)
    imgObjectHolder:Prefab = null;
    @property(JsonAsset)
    config:JsonAsset = null;
    @property(Node)
    cache:Node = null;
    @property(Node)
    newHolder:Node = null;
    @property
    deactiveObject: boolean = true;
    @property
    dupplicate: boolean = false;
    @property
    objectWantToSpawn:string = "";
    @property
    get generateShape(){
        return false;
    }
    set generateShape(v){
        this._generateShape();
    }

    @property
    get clearShape(){
        return false;
    }
    set clearShape(v){
        this.newHolder.removeAllChildren();
    }

    @property
    numberObjectWantToActive = 20;
    @property
    get activeObject(){
        return false;
    }
    set activeObject(v){
        let c = 0;
        for(let i in this.newHolder.children){
            if(!this.newHolder.children[i].active){
                this.newHolder.children[i].active = true;
                c++;
            }
            if(c >= this.numberObjectWantToActive)
            break;
        }
    }

    start() {

    }

    _generateShape(){
        for(let i in this.newHolder.children){
            const name = this.newHolder.children[i].name;
            if(this.cache.getChildByName(name)){
                if(this.dupplicate){
                    log("dupplicate object: " + this.newHolder.children[i].name);
                }else{
                    continue;
                }
            }
            this.newHolder.children[i].setParent(this.cache);
        }

        if(!this.objectWantToSpawn)
            return;
        const listId = JSON.parse(this.objectWantToSpawn);
        
        for(let j in listId){
            const id = listId[j];
            let node = this.cache.getChildByName(id+"")
            if(!!node && !this.dupplicate){
                log("dupplicate object: " + id);
                continue;
            }
            const frame = this._findSpriteFrame(id);
            if(frame == null){
                log("img id"+id+" not found/"+this.imgObjectHolder.data.children.length);
                continue;
            }
            node = this.newHolder.getChildByName(id+"") || instantiate(this.cache.getChildByName("-1"));
            node.name = id+"";
            node.active = !this.deactiveObject;
            this.newHolder.addChild(node);
            let sprite = node.getComponent(Sprite);
            if(sprite == null)
                sprite = node.addComponent(Sprite);

            sprite.spriteFrame = frame;
            node.getChildByName("img").getComponent(Sprite).spriteFrame = frame;
            node.getChildByName("maskShadow").getComponent(Sprite).spriteFrame = frame;
            node.getChildByName("maskShadow").getChildByName("shadow").getComponent(Sprite).spriteFrame = frame;
            this._generateBolt(node, this.config.json["id"+id]);
        }
    }
    _generateBolt(shape, poss){
        const screws = [];
        const screw = shape.getChildByName("screw");
        screw.position = v3(poss[0].x*100, poss[0].y*100);
        screws.push(screw.getComponent(Screw));
        for(let i = 1; i < poss.length; i++){
            let nScrew = instantiate(screw);
            nScrew.setParent(shape);
            nScrew.position = v3(poss[i].x*100, poss[i].y*100);
            screws.push(nScrew.getComponent(Screw));
        }

        this.scheduleOnce(()=>{
            for(let i in screws){
                screws[i].mapJoint = true;
            }
        })
    }

    _findSpriteFrame(id){
        const node = this.imgObjectHolder.data.getChildByName("id"+id);
        if(node == null)
            return null;
        return node.getComponent(Sprite).spriteFrame;
    }

    update(deltaTime: number) {
        
    }
}
```

## File: ShapeGenerator.ts.meta/ShapeGenerator.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "5b0f181c-ad61-40e8-b296-2a1b10d09e4c",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: StaticResources.ts/StaticResources.ts
```typescript
import { _decorator, Component, Node, SpriteFrame } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('StaticResources')
export class StaticResources extends Component {
    @property([SpriteFrame])
    boltCommand:SpriteFrame[] = [];
    @property([SpriteFrame])
    screwThread:SpriteFrame[] = [];
    @property([SpriteFrame])
    box:SpriteFrame[] = [];
    @property([SpriteFrame])
    boxLid:SpriteFrame[] = [];

    public static i : StaticResources = null;

    protected onLoad(): void {
        StaticResources.i = this;
    }

    update(deltaTime: number) {
        
    }
}
```

## File: StaticResources.ts.meta/StaticResources.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "68149180-c322-4fe5-89ee-5c09a0ff67b4",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: test/TestCamera.ts/TestCamera.ts
```typescript
import { _decorator, Camera, color, Component, log, Node, RenderTexture, screen, size, Sprite, SpriteFrame, UITransform, v3, view } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TestCamera')
export class TestCamera extends Component {
    @property(Camera)
    camera1: Camera = null;
    @property(Camera)
    camera2: Camera = null;
    @property(Sprite)
    sprite1: Sprite = null;
    @property(Sprite)
    sprite2: Sprite = null;
    @property(Node)
    cursor: Node = null;
    @property(Sprite)
    preview:Sprite = null;

    start() {
        const _size = view.getVisibleSize()// view.getDesignResolutionSize()// view.getVisibleSize()// view.getDesignResolutionSize(); //screen.windowSize;//view.getVisibleSize()
        if (!this.camera1.targetTexture) {
            const texture1 = new RenderTexture();
            texture1.reset(_size);
            this.camera1.targetTexture = texture1;
            const frame = new SpriteFrame();
            frame.texture = texture1;
            this.preview.spriteFrame = frame;
        }
        if (!this.camera2.targetTexture) {
            const texture2 = new RenderTexture();
            texture2.reset(_size);
            this.camera2.targetTexture = texture2;
        }

        this.node.on(Node.EventType.TOUCH_START, this.touchStart, this);
        this.node.on(Node.EventType.TOUCH_MOVE, this.touchMove, this);
        this.node.on(Node.EventType.TOUCH_END, this.touchEnd, this);
        this.node.on(Node.EventType.TOUCH_CANCEL, this.touchCancel, this);
    }
    touching = false;
    touchStart(e){
        this.touching = true;
        this.process(e.getUILocation())
    }
    touchMove(e) {
        this.process(e.getUILocation())
    }
    process(position) {
        let f = !!this.camera1.targetTexture && !!this.camera2.targetTexture;

        // const p = this.cursor.parent.getComponent(UITransform).convertToNodeSpaceAR(v3(position.x, position.y, 0));
        // this.cursor.position = p;
        
        this.scheduleOnce(()=>{
            const w = 1;// this.cursor.getComponent(UITransform).width;
            const h = 1;// this.cursor.getComponent(UITransform).height;
            // const b1 = this.camera1.targetTexture.readPixels(position.x, position.y, 1, 1);
            const b1 = this.camera1.targetTexture.readPixels(position.x - w/2, position.y - h/2, w, h);
            const b2 = this.camera2.targetTexture.readPixels(position.x - w/2, position.y - h/2, w, h);
            // if(b1[3] > 0 && b2[3] > 0){
                
            // }
            this.sprite1.color = color(b1[0], b1[1], b1[2], 255);
            this.sprite2.color = color(b2[0], b2[1], b2[2], 255);
            return;

            const near = function(i, w, ox, oy){
                const x = (i/4)%w + ox;
                const y = Math.floor((i/4)/w) + oy;
                return (x*4 + y*4*w);
            }
            const nears = function(i, w){
                return [
                    near(i, w, 1, 1),
                    near(i, w, 0, 1),
                    near(i, w, -1, 1),
                    near(i, w, -1, 0),
                    near(i, w, 1, 0),
                    near(i, w, -1, -1),
                    near(i, w, 0, -1),
                    near(i, w, 1, -1),
                ];
            }
            const minA = 50;
            const checkNear = function(is){
                let c = 0;
                for(let i in is){
                    if (b1[is[i] + 3] > minA && b2[is[i] + 3] > minA)
                        c++;
                }
                return c/is.length;
            }

            for(let i = 0; i < b1.length; i+= 4){
                if(b1[i+3] > minA && b2[i+3] > minA){
                    if(b1[i+3] > 200 && b2[i+3] > 200 || checkNear(nears(i, w)) > 0.5){
                        this.sprite1.color = color(b1[i + 0], b1[i + 1], b1[i + 2], b1[i + 3]);
                        return;
                    }

                }
            } 
            this.sprite1.color = color(0,0,0,0);
        })
    }
    touchEnd(){
        this.touching = false;
    }
    touchCancel(){
        this.touchEnd();
    }

    update(deltaTime: number) {
        
    }
}
```

## File: test/TestCamera.ts.meta/TestCamera.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "86ebbd2f-1da3-42a2-bb92-69af367ee400",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: test/TestCollider2D.ts/TestCollider2D.ts
```typescript
import { _decorator, Collider2D, Component, log, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TestCollider2D')
export class TestCollider2D extends Component {
    @property(Collider2D)
    collider: Collider2D = null;
    @property
    get logGroup(){
        return false;
    }
    set logGroup(v){
        if(this.collider == null)
            log("collider is null");
        else log("collider group: " + this.collider.group);
    }

    start() {

    }

    update(deltaTime: number) {
        
    }
}
```

## File: test/TestCollider2D.ts.meta/TestCollider2D.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "cf9bd880-1016-4cfd-9314-f2c14fe1176d",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: test.meta/test.meta
```
{
  "ver": "1.2.0",
  "importer": "directory",
  "imported": true,
  "uuid": "b2b1925c-4fd2-4a5a-b6f1-927e7623c73c",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```

## File: TopLayer.ts/TopLayer.ts
```typescript
import { _decorator, Component, Node } from 'cc';
const { ccclass, property } = _decorator;

@ccclass('TopLayer')
export class TopLayer extends Component {
    public static i : TopLayer = null;
    onLoad(){
        TopLayer.i = this;
    }
}
```

## File: TopLayer.ts.meta/TopLayer.ts.meta
```
{
  "ver": "4.0.23",
  "importer": "typescript",
  "imported": true,
  "uuid": "ae564727-27cf-4408-8886-fe6081cde9d8",
  "files": [],
  "subMetas": {},
  "userData": {}
}
```
