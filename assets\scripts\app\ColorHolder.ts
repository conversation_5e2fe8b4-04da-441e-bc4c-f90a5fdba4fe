import { _decorator, Component, Node } from 'cc';
import { Singleton } from '../common/Singleton';
import { BoltMapping, BoxMapping, BoxLidMapping } from '../enums/DataConfigs';
const { ccclass, property } = _decorator;

@ccclass('ColorHolder')
export class ColorHolder extends Singleton {

    @property([BoltMapping])
    public boltMappings: BoltMapping[] = [];

    @property([BoxMapping])
    public boxMappings: BoxMapping[] = [];

    @property([BoxLidMapping])
    public boxLidMappings: BoxLidMapping[] = [];

    public static get Instance(): ColorHolder {
        return this.getInstance();
    }
}


