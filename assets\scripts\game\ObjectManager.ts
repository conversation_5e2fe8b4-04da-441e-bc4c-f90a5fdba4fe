import { _decorator, CCInteger, Component, instantiate, Node } from 'cc';
import { LevelDataConfig, ObjectDataConfig, ObjectPrefabMapping } from '../enums/DataConfigs';
import { ObjectComponent } from './Components/ObjectComponent';
import { Bolt } from './Components/Bolt';
const { ccclass, property } = _decorator;

@ccclass('ObjectManager')
export class ObjectManager extends Component {
    @property([ObjectPrefabMapping])
    public objects: ObjectPrefabMapping[] = [];

    @property(CCInteger)
    public layerDisplay: number = 0;

    public bolts: Bolt[] = [];
    public initialize(levelConfig: LevelDataConfig) {

        const shapeConfigs = levelConfig.ObjectDatas;
        this.node.removeAllChildren();

        const sortedConfigs = [...shapeConfigs].sort((a, b) => b.layer - a.layer);

        for (let i = 0; i < sortedConfigs.length; i++) {
            const config = sortedConfigs[i];

            if (config.layer < this.layerDisplay) {
                continue;
            }

            const prefab = this.objects.find(obj => obj.shapeID === config.Type).prefab;
            if (prefab) {
                const obj = instantiate(prefab);
                obj.setParent(this.node);
                obj.setPosition(config.Position);
                obj.angle = config.Rotation.x;

                const objectComponent = obj.getComponent(ObjectComponent);
                if (objectComponent) {
                    objectComponent.initialize(config, levelConfig.ColorShapes[config.layer - 1]);

                    for (const bolt of objectComponent.getBolts()) {
                        this.bolts.push(bolt);
                    }
                }
            }
        }


    }
}


