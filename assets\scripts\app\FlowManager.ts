import { _decorator, Component } from 'cc';
import { GameManager } from './GameManager';
import { SoundManager } from './SoundManager';
const { ccclass, property } = _decorator;

const mraid = window['mraid'] || null;

@ccclass('FlowManager')
export class FlowManager extends Component {

    public static instance: FlowManager = null;
    public static isLoaded = false;

    protected onLoad(): void {
        FlowManager.instance = this;
        window['gameReady'] && window['gameReady']();
    }

    protected start(): void {
        this.waitSettingUp(window['advChannels']);
    }

    private waitSettingUp(ad_network): void {
        switch (ad_network) {
            case "Unity": {
                // console.log('init case Unity');

                if (mraid) {
                    this.initUnity();
                }
                else {
                    // console.log('mraid not found');
                    this.init();
                }

                break;
            }
            case "Mintegral": {
                //call from window["gameStart"]
                // console.log('init case Mintegral lately');

                if (FlowManager.isLoaded) {
                    this.init();
                }
                break;
            }
            default: {
                //  console.log('init case Default');
                this.init();
                break;
            }
        }
    }

    public initUnity(): void {
        if (!!mraid) {
            this.init();
            return;
        }
        mraid.addEventListener('ready', () => {
            if (mraid?.isViewable()) {
                this.init();
            } else {
                mraid?.addEventListener('viewableChange', () => {
                    this.init();
                });
            }
        });
    }

    public init(): void {
        GameManager.Instance.startTutorial();
        // GameManager.Instance.showPicture();
    }
}


window['advChannels'] = "{{__adv_channels_adapter__}}";

window["gameStart"] = function () {
    console.log('game start');
    if (!FlowManager.instance) {
        FlowManager.isLoaded = true;
    }
    else {
        FlowManager.instance.init();
    }

};
window["gameClose"] = function () {
    SoundManager.Instance.stopBgm();
};