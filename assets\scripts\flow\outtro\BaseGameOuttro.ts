import { _decorator, Component, Node, sys, tween, UIOpacity } from 'cc';
import { FlowUnit } from '../FlowUnit';
const { ccclass, property } = _decorator;

@ccclass('BaseGameOuttro')
export class BaseGameOuttro extends FlowUnit {

    public hasTapped = false;
    public isLose = false;

    show(data?) {
        this.node.active = true;

        // console.log("open url");
        const opacity = this.node.getComponent(UIOpacity);
        if (opacity) {
            opacity.opacity = 0;
            tween(opacity).to(0.5, { opacity: 255 }).start();
        }

        const ad_network = window['advChannels'];
        if (!!ad_network) {
            switch (ad_network) {
                case "Mintegral": {
                    window['gameEnd'] && window['gameEnd']();
                    return;
                }
            }
        }


        setTimeout(() => {
            if (!this.isLose) return;
            if (this.hasTapped) return;
            this.cta();
        }, 3000);
    }

    cta() {

        this.hasTapped = true;
        const ad_network = window['advChannels'];
        if (!!ad_network) {
            switch (ad_network) {
                case "Mintegral": {
                    window['install'] && window['install']();
                    return;
                }
                case "Unity":
                case "AppLovin": {
                    let open;
                    if (sys.os == sys.OS.ANDROID) {
                        open = window["mraidOpenPlayStore"];
                    } else {
                        open = window["mraidOpenAppStore"];
                    }
                    open?.();
                    return;
                };
            }
        }
        sys.openURL("https://play.google.com/store/apps/details?id=com.legendarylabs.wool.sort");
    }
}


