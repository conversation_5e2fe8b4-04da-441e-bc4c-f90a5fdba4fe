import { _decorator, Component, Node, Prefab, instantiate } from 'cc';
import { Singleton } from '../common/Singleton';
import { SimpleLineRender } from '../game/Components/SimpleLineRender';
import { ColorType } from '../enums/Enums';
const { ccclass, property } = _decorator;

@ccclass('RopeManager')
export class RopeManager extends Singleton {
    
    @property(Prefab)
    public LinePrefab: Prefab = null;

    private ropePool: SimpleLineRender[] = [];

    

    public static get Instance(): RopeManager {
        return this.getInstance();
    }

    public getRope(): SimpleLineRender {
        const ropeNode = instantiate(this.LinePrefab);
        this.node.addChild(ropeNode); // Assuming RopeManager is a Component attached to a Node
        const ropeComponent = ropeNode.getComponent(SimpleLineRender);
        return ropeComponent;
    }

    public putRope(rope: SimpleLineRender): void {
        rope.node.active = false;
        this.ropePool.push(rope);
    }
}


