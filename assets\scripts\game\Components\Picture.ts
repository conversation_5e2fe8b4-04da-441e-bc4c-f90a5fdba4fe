import { _decorator, Color, Component, instantiate, JsonAsset, Node, Prefab, Sprite, tween, Vec2, Vec3 } from 'cc';
import { ColorType } from '../../enums/Enums';
const { ccclass, property } = _decorator;

@ccclass('Picture')
export class Picture extends Component {
    @property(Prefab)
    public pixelPrefab: Prefab = null;

    private boardSize: number = 32;
    private board: Node[][] = [];

    public initializeBoard(): void {
        this.node.removeAllChildren();

        this.board = [];
        for (let x = 0; x < this.boardSize; x++) {
            this.board[x] = [];
            for (let y = 0; y < this.boardSize; y++) {
                const pixel = instantiate(this.pixelPrefab);
                pixel.setParent(this.node);
                this.board[x][y] = pixel;
            }
        }
    }

    public getSprite(pos: Vec2): Sprite {
        const pixel = this.board[pos.x][pos.y];
        if (pixel) {
            return pixel.getComponent(Sprite);
        }

        return null;
    }

    public setColor(pos: Vec2, color: Color): void {
        const pixel = this.board[pos.x][pos.y];
        if (pixel) {
            pixel.setScale(new Vec3(0.05, 0.05, 0.05));
            pixel.getComponent(Sprite).color = color;

            tween(pixel)
                .delay(0.1)
                .to(0.3, { scale: new Vec3(1.4, 1.4, 1.4) }, { easing: 'backOut' })
                .to(0.2, { scale: new Vec3(0.9, 0.9, 0.9) }, { easing: 'quadIn' })
                .to(0.15, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
                .start();
        }
    }

    public previewPicture(pictureData: JsonAsset): void {
        const data = pictureData.json;
        if (!data) return;

        const { colorInfos } = data;
        const { positionInfos } = data;

        for (let i = 0; i < positionInfos.length; i++) {
            const colorInfo = colorInfos[i];
            const color = new Color(colorInfo.color.r * 255, colorInfo.color.g * 255, colorInfo.color.b * 255, colorInfo.color.a * 255);
            const { positions } = positionInfos[i];

            for (let j = 0; j < positions.length; j++) {
                const { x, y } = positions[j];
                this.setColor(new Vec2(x, y), color);
            }
        }

    }

   
}


