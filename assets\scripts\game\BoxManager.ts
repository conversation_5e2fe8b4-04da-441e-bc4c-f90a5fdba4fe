import { _decorator, Component, Node, Prefab, instantiate, UITransform, tween, Vec3, easing, director } from 'cc';
import { Box } from './Components/Box';
import { BoxDataConfig } from '../enums/DataConfigs';
import { ColorType } from '../enums/Enums';
import { GameManager } from '../app/GameManager';
import { SoundManager } from '../app/SoundManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('BoxManager')
export class BoxManager extends Component {

    @property(Prefab)
    public boxPrefab: Prefab = null;

    private _boxes: BoxDataConfig[] = [];
    private _currentBoxIndex: number = 0;
    private _currentBox: Box | null = null;
    private _nextBox: Box | null = null;
    private _collectedBox: Box | null = null;
    private _spacing: number = 0;

    private arrColorKey: string[] = ["9_0", "6_0", "4_0", "8_0", "4_1", "7_0", "6_1"];
    public filling: boolean = false;

    public initialize(boxes: BoxDataConfig[]): void {

        this._boxes = boxes;
        this.arrColorKey = this.getDataString(this._boxes.map(box => box.colorType));

        // console.log(this.arrColorKey);

        this._currentBoxIndex = 0;
        this._currentBox = null;
        this._nextBox = null;
        this._collectedBox = null;


        // Gán spacing bằng 1/2 chiều rộng của node
        const containerTransform = this.node.getComponent(UITransform);
        if (containerTransform) {
            this._spacing = containerTransform.width / 2;
        }

        this.updateVisual();
    }

    private createBox(boxData: BoxDataConfig | undefined, parent: Node, position: Vec3, boxID: string): Box | null {
        if (!boxData) return null;
        const boxInstance = instantiate(this.boxPrefab);
        boxInstance.setParent(parent);
        boxInstance.setPosition(position);
        const boxComp = boxInstance.getComponent(Box);

        boxComp?.setVisual(boxData, boxID);
        return boxComp;
    }

    private updateVisual(): void {
        this.node.removeAllChildren();
        if (this._boxes.length <= 0) return;

        // Current box ở giữa
        const center = new Vec3(0, 0, 0);
        this._currentBox = this.createBox(this._boxes[this._currentBoxIndex], this.node, center, this.arrColorKey[this._currentBoxIndex]);


        if (this._boxes[this._currentBoxIndex + 1]) {
            const left = new Vec3(-this._spacing, 0, 0);
            this._nextBox = this.createBox(this._boxes[this._currentBoxIndex + 1], this.node, left, this.arrColorKey[this._currentBoxIndex + 1]);
        } else {
            this._nextBox = null;
        }
        this._collectedBox = null;
    }

    public collectBox(): void {


        this._currentBoxIndex++;
        this._collectedBox = this._currentBox;
        this._currentBox = this._nextBox;

        // Tạo nextBox mới ở bên trái currentBox
        let nextBox: Box | null = null;
        if (this._boxes[this._currentBoxIndex + 1] && this._currentBox) {
            const left = new Vec3(this._currentBox.node.position.x - this._spacing, 0, 0);
            nextBox = this.createBox(this._boxes[this._currentBoxIndex + 1], this.node, left, this.arrColorKey[this._currentBoxIndex + 1]);
        }
        this._nextBox = nextBox;

        // Tween cả 3 box sang phải _spacing
        [this._collectedBox, this._currentBox, this._nextBox].forEach(box => {
            if (box) {
                const pos = box.node.position;
                tween(box.node)
                    .to(0.3, { position: new Vec3(pos.x + this._spacing, pos.y, pos.z) }, {
                        easing: 'backOut'
                    })
                    .start();
            }
        });

        // Destroy collectedBox sau khi tween xong


        SoundManager.Instance.playSfx(SoundManager.Instance.Wrap);

        if (this._collectedBox) {
            tween(this._collectedBox.node)
                .delay(0.3)
                .call(() => {
                    this._collectedBox?.node.destroy();
                    this._collectedBox = null;

                    SoundManager.Instance.playSfx(SoundManager.Instance.NextBox);


                    const bolts = GameManager.Instance.boardController.holeManager.getBoltFormHole();
                    if (bolts.length > 0) {

                        // console.log(bolts);
                        GameManager.Instance.boardController.holeManager.resetCountBoltProcess();
                    }
                    else {
                        this.
                    }

                    if (this._currentBoxIndex === this._boxes.length) {
                        // console.log("Win");

                        tween(this.node)
                            .delay(0.3)
                            .call(() => {
                                if (GameManager.Instance.isTutorial) {
                                    SoundManager.Instance.playSfx(SoundManager.Instance.Complete_Level);

                                    GameManager.Instance.showRemarkable();
                                    return;
                                }
                                else {
                                    SoundManager.Instance.playSfx(SoundManager.Instance.Complete_Level);
                                    GameManager.Instance.showWin();
                                    return;
                                }

                            })
                            .start();
                    }
                })
                .start();
        }
    }

    public get currentBox(): Box {
        return this._currentBox;
    }

    public drawBoxComple(): void {

        this.collectBox();

        const bolts = GameManager.Instance.boardController.holeManager.getBoltFormHole();
        if (bolts.length > 0) {

            console.log(bolts);
            GameManager.Instance.boardController.holeManager.resetCountBoltProcess();
        }

    }

    private getDataString(arrColor: ColorType[]): string[] {
        const result: string[] = [];
        const colorCounts: Map<ColorType, number> = new Map();

        for (const color of arrColor) {
            const count = colorCounts.get(color) || 0;
            result.push(`${color}_${count}`);
            colorCounts.set(color, count + 1);
        }

        return result;
    }


    private callBackCompleteBox() {
        GameManager.Instance.setGameState(GameState.Playing);

        const lose = GameManager.Instance.boardController.holeManager.Lose();
        if (lose) {
            SoundManager.Instance.playSfx(SoundManager.Instance.Lose);
            GameManager.Instance.showLose();
        }
    }

}


