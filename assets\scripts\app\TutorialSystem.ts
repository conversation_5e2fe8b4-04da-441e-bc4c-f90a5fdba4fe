import { _decorator, AnimationComponent, CCInteger, Component, Node, Vec3 } from 'cc';
import { Bolt } from '../game/Components/Bolt';
import { GameManager } from './GameManager';
const { ccclass, property } = _decorator;

@ccclass('TutorialSystem')
export class TutorialSystem extends Component {
    @property(AnimationComponent) private hand: AnimationComponent;
    @property(Node) nodeContainHand: Node;

    @property(CCInteger) private timeWaitngTime: number = 3;
    private timer: number = 0;

    private isActive: boolean = false;

    protected onLoad(): void {
        this.nodeContainHand.active = false;

        Bolt.onTap.on(this.hideFirstStep, this);
    }

    update(deltaTime: number) {
        if (this.isActive) {
            this.timer -= deltaTime;

            if (this.timer <= 0) {

                const currentBox = GameManager.Instance.boardController.boxManager.currentBox;
                const currentColor = currentBox.colorType;

                const arrBolts = GameManager.Instance.ObjectManager.bolts.filter(bolt => !bolt.isDestroyed && !bolt.isCollected && bolt.colorType === currentColor && !bolt.isOverlappingWithAnyCollider());
                if (arrBolts.length > 0) {
                    const firstBolt = arrBolts[0];
                    console.log('firstBolt', firstBolt.node.worldPosition);
                    this.playAnimFirstStep(firstBolt.node.worldPosition);
                }


                this.isActive = false;
            }

        }
    }

    ondestroy(): void {
        Bolt.onTap.off(this.hideFirstStep);
    }

    public playAnimFirstStep(pos: Vec3) {
        // this.hand.play("cta_hand_ring");

        this.nodeContainHand.setWorldPosition(pos);
        this.nodeContainHand.active = true;


    }


    private hideFirstStep() {
        this.nodeContainHand.active = false;

        this.timer = this.timeWaitngTime;
        this.isActive = true;
    }
}


