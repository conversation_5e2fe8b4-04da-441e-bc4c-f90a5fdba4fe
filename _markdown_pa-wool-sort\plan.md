Tuyệt vời! Tập trung vào Gameplay Cốt Lõi là một khởi đầu rất tốt. Dưới đây là kế hoạch chi tiết các bước để bạn thực hiện phần Gameplay Cốt Lõi của game này trên Cocos Creator:

**Kế Hoạch Chi Tiết Thực Hiện Gameplay Cốt Lõi**

**Mục tiêu của giai đoạn này:** Tạo ra một phiên bản có thể chơi đượ<PERSON>, nơi người chơi có thể click vào ốc vít, ốc vít đó sẽ được thu thập vào một hộp phù hợp, và có cơ chế kiểm tra khi hộp đầy.

**Các <PERSON>dule <PERSON>ên <PERSON>uan (Từ Kế Hoạch Tổng Thể):**
*   Module 1: Thiết Lập Dự Án và Cấu Trúc <PERSON> (một phần)
*   Module 2: Gameplay Cốt Lõi - Ốc Vít và Hộp
*   Module 3: Gameplay Cốt Lõi - Vật Thể Game và Tương Tác (một phần)

---

**Bước 1: Thiết Lập Dự Án và Tài Nguyên Cơ Bản**

1.  **Tạo Dự Án Mới trong Cocos Creator:**
    *   Chọn template "Empty (2D)".
    *   Thiết lập cấu trúc thư mục cơ bản:
        *   `assets/Scripts/`
        *   `assets/Prefabs/`
        *   `assets/Textures/` (hoặc `assets/Sprites/`)
        *   `assets/Scenes/`
2.  **Import Tài Nguyên Hình Ảnh Ban Đầu (Placeholders):**
    *   **Ốc vít (Screw):**
        *   `bolt_color_1.png` (phần đầu ốc vít, ví dụ màu đỏ)
        *   `thread_default.png` (phần ren ốc)
    *   **Hộp (Box):**
        *   `box_body_color_1.png` (thân hộp, ví dụ màu đỏ)
        *   `box_lid_color_1.png` (nắp hộp, ví dụ màu đỏ)
    *   **Vật thể game (GameObject):**
        *   `plate_default.png` (một tấm kim loại hoặc gỗ đơn giản)
        *   `shadow_default.png` (một sprite bóng mờ)
    *   *Lưu ý: Ban đầu chỉ cần 1-2 màu để thử nghiệm.*
3.  **Tạo Script `StaticResources.ts`:**
    *   **Mục đích:** Quản lý tập trung các SpriteFrame.
    *   **Triển khai:**
        *   Tạo script `StaticResources.ts` trong thư mục `Scripts`.
        *   Khai báo các mảng `SpriteFrame` public: `boltCommand`, `screwThread`, `box`, `boxLid`.
        *   Tạo một Node trong Scene (ví dụ tên là "StaticResourcesNode"), gắn script `StaticResources` vào đó.
        *   Kéo thả các SpriteFrame từ `assets/Textures/` vào các thuộc tính tương ứng trong Inspector của "StaticResourcesNode".
        *   Thêm một biến static `public static i : StaticResources = null;` và gán `StaticResources.i = this;` trong `onLoad()`.
4.  **Tạo Script `GameConfig.ts`:**
    *   **Mục đích:** Định nghĩa ID màu và các cấu hình liên quan.
    *   **Triển khai:**
        *   Tạo script `GameConfig.ts`.
        *   Định nghĩa một enum hoặc object đơn giản cho ID màu (ví dụ: `RED = 0`, `BLUE = 1`).
        *   *Ban đầu chưa cần các hàm phức tạp lấy màu ngẫu nhiên.*

---

**Bước 2: Tạo Prefab và Script cho Ốc Vít (Screw)**

1.  **Tạo Prefab `ScrewPrefab`:**
    *   Tạo một Node mới trong Scene, đặt tên là "Screw".
    *   **Cấu trúc Node "Screw":**
        *   `Screw` (Node gốc)
            *   `Bolt` (Node con, chứa Sprite cho đầu ốc vít)
            *   `Thread` (Node con, chứa Sprite cho ren ốc)
    *   Gắn component `Sprite` cho `Bolt` và `Thread`. Gán SpriteFrame placeholder đã import.
    *   Điều chỉnh vị trí tương đối của `Bolt` và `Thread` cho hợp lý.
    *   Thêm component `Button` vào Node `Screw` (để có thể click).
    *   Thêm component `Fake3D.ts` (sẽ tạo ở bước sau) vào Node `Screw`.
    *   Kéo Node "Screw" từ Hierarchy vào thư mục `Prefabs` để tạo `ScrewPrefab`.
2.  **Tạo Script `Fake3D.ts`:**
    *   **Mục đích:** Tạo hiệu ứng chiều sâu cho ren ốc.
    *   **Triển khai:**
        *   Tạo script `Fake3D.ts`.
        *   Khai báo property `target: Node` (trỏ đến Node `Thread`).
        *   Khai báo property `z: number`.
        *   Trong `set z(v)`, cập nhật `target.position.y` và `target.scale` dựa trên giá trị `z`.
3.  **Tạo Script `Screw.ts`:**
    *   **Mục đích:** Quản lý logic của ốc vít.
    *   **Triển khai:**
        *   Gắn script này vào `ScrewPrefab`.
        *   Khai báo các property:
            *   `bolt: Sprite` (kéo Node `Bolt` vào).
            *   `thread: Sprite` (kéo Node `Thread` vào).
            *   `_boltId: number`.
        *   Trong `set boltId(v)`:
            *   Cập nhật `this.bolt.spriteFrame = StaticResources.i.boltCommand[this._boltId];`
            *   Cập nhật `this.thread.spriteFrame = StaticResources.i.screwThread[this._boltId];` (ban đầu có thể chỉ có 1 loại ren).
        *   Trong `lateUpdate()`:
            *   Lấy component `Fake3D`.
            *   Cập nhật `this.thread.getComponent(UITransform).height` dựa trên `fake3D.z`.
        *   Viết hàm `onClick()`:
            *   `console.log("Screw clicked, ID:", this.boltId);` (chưa cần logic phức tạp).
            *   Gán hàm này cho sự kiện `clickEvents` của `Button` trên `ScrewPrefab`.

---

**Bước 3: Tạo Prefab và Script cho Hộp (GameBox)**

1.  **Tạo Prefab `GameBoxPrefab`:**
    *   Tạo một Node mới, đặt tên là "GameBox".
    *   **Cấu trúc Node "GameBox":**
        *   `GameBox` (Node gốc)
            *   `Body` (Node con, chứa Sprite cho thân hộp)
            *   `Lid` (Node con, chứa Sprite cho nắp hộp)
            *   `ScrewHolder` (Node con, là một Node trống để chứa các ốc vít đã thu thập)
            *   `Slot1`, `Slot2`, `Slot3` (Node con của `ScrewHolder`, là các Node trống định vị vị trí ốc vít, ví dụ 3 slot)
    *   Gắn `Sprite` cho `Body` và `Lid`, gán SpriteFrame placeholder.
    *   Điều chỉnh vị trí `Lid` để có thể che/mở `Body`.
    *   Kéo Node "GameBox" vào `Prefabs` để tạo `GameBoxPrefab`.
2.  **Tạo Script `GameBox.ts`:**
    *   **Mục đích:** Quản lý logic của một hộp.
    *   **Triển khai:**
        *   Gắn script này vào `GameBoxPrefab`.
        *   Khai báo các property:
            *   `imgs: Sprite[]` (kéo `Body` vào, có thể mở rộng sau).
            *   `lids: Sprite[]` (kéo `Lid` vào).
            *   `slots: Node[]` (kéo `Slot1`, `Slot2`, `Slot3` vào).
            *   `holder: Node` (kéo `ScrewHolder` vào).
            *   `_boxId: number`.
            *   `screws: Screw[] = []` (để lưu trữ các ốc vít đã thu thập).
            *   `ready: boolean = false`.
        *   Trong `set boxId(v)`:
            *   Cập nhật `this.imgs[0].spriteFrame = StaticResources.i.box[this._boxId];`
            *   Cập nhật `this.lids[0].spriteFrame = StaticResources.i.boxLid[this._boxId];`
        *   Hàm `init(id: number)`:
            *   Gán `this.boxId = id;`.
            *   Reset `this.screws = [];`.
            *   Đảm bảo các `slots` chưa bị chiếm (`slot["occupied"] = false;`).
        *   Hàm `getSlot(): Node | null`:
            *   Tìm và trả về slot trống đầu tiên.
        *   Getter `get full(): boolean`:
            *   Kiểm tra xem `this.screws.length` có bằng `this.slots.length` không.
        *   Getter `get fulled(): boolean`:
            *   Kiểm tra xem tất cả ốc vít trong `this.screws` đã `collected` chưa và có `full` không.
        *   Hàm `putScrew(screw: Screw)`:
            *   Thêm `screw` vào mảng `this.screws`.
        *   Hàm `closeLid()` (ban đầu có thể chỉ là `this.lids[0].node.active = true;`).
        *   Hàm `openLid()` (ban đầu có thể chỉ là `this.lids[0].node.active = false;`).

---

**Bước 4: Tạo Script Quản Lý Hộp (BoxHolder)**

1.  **Tạo Script `BoxHolder.ts`:**
    *   **Mục đích:** Quản lý các `GameBox` và logic thu thập ốc vít chính.
    *   **Triển khai:**
        *   Tạo một Node trong Scene tên là "BoxHolderNode", gắn script này vào.
        *   Khai báo các property:
            *   `box: GameBox` (Kéo một `GameBoxPrefab` vào Scene, rồi kéo Node đó vào đây. Đây là hộp chính người chơi tương tác).
            *   `queue: GameBox[]` (Mảng các `GameBoxPrefab` khác, là hàng đợi. Ban đầu có thể để trống hoặc 1-2 hộp).
            *   `temp: GameBox` (Kéo `GameBoxPrefab` vào đây, dùng để instantiate).
            *   `holder: Node` (Node cha để chứa các `GameBox` trong hàng đợi, nếu cần).
        *   Biến static `public static i : BoxHolder = null;` và gán trong `onLoad()`.
        *   Biến `playing: boolean = true;`.
        *   Biến `data: any = {};` (lưu trữ số lượng ốc vít cần cho mỗi màu, ví dụ `{'0': 2, '1': 1}`).
        *   Hàm `init(boxData: any, firstColor: number = -1)`:
            *   Gán `this.data = boxData;`.
            *   Khởi tạo `this.box` với `firstColor` (nếu có) hoặc màu ngẫu nhiên từ `boxData`.
            *   Khởi tạo các `GameBox` trong `queue`.
        *   Hàm `getBoxColor(): number`:
            *   Lấy một màu từ `this.data` và giảm số lượng.
        *   Hàm `newBox()`:
            *   Khi hộp hiện tại đầy, lấy hộp tiếp theo từ `queue` làm hộp chính.
            *   Tạo một hộp mới vào cuối `queue`.
        *   Hàm `checkFullBox(box: GameBox)`:
            *   Nếu `box.fulled` là `true`:
                *   Nếu `box` là `this.box` (hộp chính), gọi `this.newBox()`.
                *   `console.log("Box fulled!");`
        *   Hàm `onCollectBolt(box: GameBox, screw: Screw)`:
            *   `console.log("Bolt collected for box:", box.boxId);`
            *   *Sau này sẽ thêm logic kiểm tra thắng/thua ở đây.*
        *   Hàm `getBox(boltId: number): GameBox | null`:
            *   Kiểm tra xem `this.box` có `ready`, chưa `full`, và `boxId` khớp với `boltId` không. Nếu có, trả về `this.box`.
            *   *Sau này có thể mở rộng để lấy từ `queue` nếu cần.*

---

**Bước 5: Tạo Vật Thể Game (GameObject) và Gắn Ốc Vít**

1.  **Tạo Prefab `GameObjectPrefab`:**
    *   Tạo Node mới "GameObject".
    *   **Cấu trúc Node "GameObject":**
        *   `GameObject` (Node gốc)
            *   `Img` (Node con, Sprite cho hình ảnh chính)
            *   `Shadow` (Node con, Sprite cho bóng)
            *   (Gắn các `ScrewPrefab` làm con của `GameObject` này, ví dụ 2-3 ốc vít)
    *   Gắn `Sprite` cho `Img` và `Shadow`, gán SpriteFrame placeholder.
    *   Thêm `PolygonCollider2D` (hoặc `BoxCollider2D`) vào `Img` (hoặc `GameObject` gốc).
    *   Thêm `RigidBody2D` vào `GameObject` gốc, đặt `type` là `Static` (hoặc `Kinematic` nếu cần di chuyển).
    *   Kéo vào `Prefabs`.
2.  **Tạo Script `GameObject.ts`:**
    *   **Mục đích:** Quản lý logic của một vật thể game.
    *   **Triển khai:**
        *   Gắn script này vào `GameObjectPrefab`.
        *   Khai báo property:
            *   `img: Sprite` (kéo Node `Img` vào).
            *   `shadow: Sprite` (kéo Node `Shadow` vào).
            *   `body: Collider2D` (kéo Collider2D vào).
        *   Trong `lateUpdate()`:
            *   Cập nhật vị trí của `shadow.node` so với `img.node` để tạo hiệu ứng bóng đổ đơn giản.
3.  **Cập nhật `Screw.ts`:**
    *   Thêm property `joint: Joint2D`.
    *   Trong `start()` hoặc `onLoad()`:
        *   Lấy `Joint2D` component.
        *   Gán `this.joint.connectedBody = this.node.parent.getComponent(RigidBody2D);` (nếu `Screw` là con trực tiếp của `GameObject`).
        *   Gán `this.joint.connectedAnchor` dựa trên vị trí của `Screw` so với `GameObject`.
    *   Hàm `leaveObject()`:
        *   `if (this.joint) this.joint.enabled = false;`

---

**Bước 6: Tạo Scene Gameplay và Kết Nối Logic**

1.  **Tạo Scene `GameplayScene`:**
    *   Xóa Scene mặc định (nếu có).
    *   Thêm "StaticResourcesNode" và "BoxHolderNode" vào Scene.
    *   Kéo một vài `GameObjectPrefab` vào Scene.
    *   **Thiết lập cho các `Screw` trên `GameObjectPrefab`:**
        *   Gán `boltId` cho từng `Screw` (ví dụ, tất cả là màu đỏ `0`).
    *   **Thiết lập cho "BoxHolderNode":**
        *   Trong Inspector của `BoxHolder`, kéo `GameBoxPrefab` vào `temp`.
        *   Kéo một `GameBoxPrefab` đã đặt trong Scene vào `box` của `BoxHolder`.
        *   Gọi `BoxHolder.i.init({'0': 5});` trong `start()` của một script quản lý Scene (hoặc trực tiếp trong `BoxHolder` để test). Điều này nghĩa là cần thu thập 5 ốc vít màu đỏ.
2.  **Cập nhật `Screw.ts` - Logic `onClick()`:**
    *   ```typescript
      onClick() {
          if (!BoxHolder.i || !BoxHolder.i.playing) return;

          // Tạm thời chưa cần checkBlock
          // this.checkBlock().then(block => {
          //     if (!block) {
                  const targetBox = BoxHolder.i.getBox(this.boltId);
                  if (!targetBox || !targetBox.ready) {
                      console.log("No suitable box or box not ready for boltId:", this.boltId);
                      return;
                  }

                  const slotNode = targetBox.getSlot();
                  if (!slotNode) {
                      console.log("Box is full, cannot add boltId:", this.boltId);
                      return;
                  }

                  this.leaveObject();
                  this.getComponent(Button).interactable = false;
                  targetBox.putScrew(this);
                  slotNode["occupied"] = true; // Đánh dấu slot đã bị chiếm

                  // --- Logic "bay" của ốc vít ---
                  // 1. Chuyển Screw sang TopLayer (tạo Node TopLayer nếu chưa có)
                  // const topLayerNode = find("TopLayerNode"); // Cần có Node này trong Scene
                  // if (topLayerNode) {
                  //     const worldPos = this.node.parent.getComponent(UITransform).convertToWorldSpaceAR(this.node.position);
                  //     this.node.parent = topLayerNode;
                  //     this.node.position = topLayerNode.getComponent(UITransform).convertToNodeSpaceAR(worldPos);
                  // }

                  // 2. Tween ốc vít đến vị trí slotNode trong targetBox.holder
                  const worldSlotPos = slotNode.parent.getComponent(UITransform).convertToWorldSpaceAR(slotNode.position);
                  const targetLocalPosInHolder = targetBox.holder.getComponent(UITransform).convertToNodeSpaceAR(worldSlotPos);

                  tween(this.node)
                      .to(0.5, { position: targetLocalPosInHolder }) // Giả sử Screw đã là con của targetBox.holder
                      .call(() => {
                          this.node.parent = targetBox.holder; // Đảm bảo là con của holder
                          this.node.position = targetLocalPosInHolder; // Chỉnh lại vị trí chính xác
                          this.collected = true; // Đánh dấu đã thu thập
                          BoxHolder.i.checkFullBox(targetBox);
                          BoxHolder.i.onCollectBolt(targetBox, this);
                      })
                      .start();

                  // (Tùy chọn) Tween Fake3D.z và angle
                  const fake3D = this.getComponent(Fake3D);
                  if (fake3D) {
                      tween(fake3D).to(0.3, { z: 1000 }).to(0.2, { z: 0 }, { easing: "sineIn" }).start();
                  }
                  tween(this.bolt.node).by(0.5, { angle: 720 }).start();
          //     }
          // });
      }
      ```
    *   *Lưu ý: Phần `TopLayer` và logic `fly` chi tiết có thể cần một Node UI riêng (`TopLayer.ts`) để quản lý việc hiển thị ốc vít trên cùng khi bay.* Ban đầu, bạn có thể đơn giản hóa bằng cách cho ốc vít `destroy()` và tạo một ốc vít mới ở vị trí slot.
3.  **Tạo Node `TopLayerNode` (nếu cần cho hiệu ứng bay):**
    *   Tạo một Node UI (Canvas -> UI Node) trong Scene, đặt tên là "TopLayerNode".
    *   Đảm bảo Node này render trên cùng.
    *   Tạo script `TopLayer.ts` và gắn vào, thêm biến static `i` như các script quản lý khác.

---

**Bước 7: Kiểm Thử và Tinh Chỉnh**

1.  **Chạy Game:**
    *   Click vào các ốc vít.
    *   Quan sát Console Log để xem các thông báo.
    *   Kiểm tra xem ốc vít có được thêm vào đúng `GameBox` không.
    *   Kiểm tra xem `BoxHolder` có nhận diện được khi hộp đầy không.
2.  **Debug và Sửa Lỗi:**
    *   Sử dụng công cụ Debug của Cocos Creator.
    *   Kiểm tra các tham chiếu Node trong Inspector có đúng không.
    *   Điều chỉnh vị trí, kích thước của các Sprite.
3.  **Tinh Chỉnh:**
    *   Điều chỉnh tốc độ tween, easing cho hiệu ứng bay (nếu đã làm).
    *   Thêm các hiệu ứng âm thanh cơ bản (click, thu thập) bằng `AudioManager`.

---

**Kết quả mong đợi sau khi hoàn thành các bước này:**

*   Người chơi có thể click vào các ốc vít có màu/ID cụ thể.
*   Ốc vít sẽ được "thu thập" vào một hộp đang chờ có màu/ID tương ứng.
*   Hệ thống nhận biết được khi một hộp đã đầy ốc vít.
*   Có các Prefab cơ bản cho Screw, GameBox, GameObject.
*   Cấu trúc script cơ bản đã được thiết lập.

Đây là một nền tảng vững chắc để bạn tiếp tục xây dựng các tính năng phức tạp hơn như `GameCamera` để check block, hệ thống `FlowManager`, `MapGenerator`, và meta-game. Chúc bạn thành công!