import { _decorator, Color, Component, Node, Sprite, Sprite<PERSON>rame, Vec3, tween, CCString, Vec2, UITransform, Size } from 'cc';
import { BoxDataConfig } from '../../enums/DataConfigs';
import { ColorHolder } from '../../app/ColorHolder';
import { ColorType } from '../../enums/Enums';
import { RopeManager } from '../../app/RopeManager';
import { BoardController } from '../BoardController';
import { GameManager } from '../../app/GameManager';
import { SoundManager } from '../../app/SoundManager';
import { BoxLid } from './BoxLid';
import { GameState } from '../../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('Box')
export class Box extends Component {

    @property(Sprite)
    public img: Sprite = null;

    @property(Node)
    public ropePos: Node = null;

    @property(Node)
    public boxLids: Node = null;



    @property(CCString)
    public boxID: string = "";

    private _slot: number = 0;
    private _color: ColorType = ColorType.None;
    private _spriteFrame: SpriteFrame = null;
    private _boxLids: BoxLid[] = [];

    private _lidCount: number = 0;
    private _lidCountMax: number = 4;

    private _lidComplete: number = 0;

    private contentSize: number = 74;
    private lidSize: number = 50;

    public setVisual(boxData: BoxDataConfig, boxID: string = ""): void {
        this.img.spriteFrame = ColorHolder.Instance.boxMappings.find(mapping => mapping.colorType === boxData.colorType).spriteFrame;
        this._slot = boxData.slot;
        this._lidCountMax = boxData.slot;
        this._lidCount = 0;
        this._color = boxData.colorType;

        this._spriteFrame = ColorHolder.Instance.boxLidMappings.find(mapping => mapping.colorType === boxData.colorType).spriteFrame;

        for (let i = 0; i < this.boxLids.children.length; i++) {
            const child = this.boxLids.children[i];
            const boxLid = child.getComponent(BoxLid);
            boxLid.setupView(this._spriteFrame);
            this._boxLids.push(boxLid);

            child.active = i < this._lidCountMax;
        }

        if (!GameManager.Instance.boardController.isPart) {
            boxID = "1_0";
        }

        this.boxID = boxID;

        const size = this.img.node.getComponent(UITransform).contentSize;
        const newSize = new Size(this.contentSize + this.lidSize * this.lidCount, size.height);
        this.img.node.getComponent(UITransform).setContentSize(newSize);
    }

    public get colorType(): ColorType {
        return this._color;
    }



    public checkCompleteLid(): void {

        this._lidComplete++;
        if (this._lidComplete >= this._lidCountMax) {
            this.drawBoard();
        }
    }

    private drawBoard(): void {
        GameManager.Instance.setGameState(GameState.DrawBoard);
        SoundManager.Instance.playSfx(SoundManager.Instance.Knitt);

        const rope = RopeManager.Instance.getRope();
        rope.node.setParent(this.ropePos);

        // console.log(this.boxID);



        const { color, arrPos, arrPos2 } = GameManager.Instance.boardController.getArrPos(this.boxID);



        const colorInfo = new Color(
            color.r * 255,
            color.g * 255,
            color.b * 255,
            color.a * 255
        );

        this.clearLid()
        rope.setAnimationDuration(0.2);
        rope.createLine2(this.node.getWorldPosition().clone(), arrPos2, arrPos, colorInfo, 20, () => {
            tween(this.node)
                .to(0.15, { position: new Vec3(0, -15, 0) }, { easing: 'quadOut' })
                .to(0.25, { position: new Vec3(0, 0, 0) }, { easing: 'backOut' })
                .delay(0.2)
                .call(() => GameManager.Instance.boardController.boxManager.collectBox())
                .start();

        }, (pos) => {
            const sprite = GameManager.Instance.boardController.getSprite(pos);
            if (sprite) {
                sprite.node.setScale(new Vec3(0.05, 0.05, 0.05));
                sprite.color = colorInfo;

                tween(sprite.node)
                    .delay(0.1)
                    .to(0.3, { scale: new Vec3(1.4, 1.4, 1.4) }, { easing: 'backOut' })
                    .to(0.2, { scale: new Vec3(0.9, 0.9, 0.9) }, { easing: 'quadIn' })
                    .to(0.15, { scale: new Vec3(1, 1, 1) }, { easing: 'quadOut' })
                    .start();
            }
        });
    }

    public get lidCount(): number {
        return this._slot;
    }

    public clearLid(): void {
        for (let i = 0; i < this._boxLids.length; i++) {
            const boxLid = this._boxLids[i];
            tween(boxLid.node)
                .delay(0.4 * (this._boxLids.length - 1 - i))
                .call(() => {
                    boxLid.playAnimationDisappear();
                })
                .start();
        }
    }


    public checkCorrect(color: ColorType): boolean {
        if (this._lidCount >= this._lidCountMax) return false;

        if (this._color === color) {
            return true;
        }

        return false;
    }

    public get PosLidIndex(): Vec3 {
        return this.boxLids.children[this._lidCount].getWorldPosition();
    }

    public getBoxLid(): BoxLid {
        return this._boxLids[this._lidCount];
    }

    public IncreaseLidCount(): void {
        this._lidCount++;
    }

}


