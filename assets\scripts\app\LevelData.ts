import { _decorator, Component, JsonAsset, Node, Color, Vec3 } from 'cc';
import { LevelDataConfig, ObjectDataConfig, BoxDataConfig } from '../enums/DataConfigs';
import { GameManager } from './GameManager';
import { GameState } from '../enums/GameState';
const { ccclass, property } = _decorator;

@ccclass('LevelData')
export class LevelData extends Component {

    @property(JsonAsset)
    public data: JsonAsset = null;

    @property([LevelDataConfig])
    public levelData: LevelDataConfig[] = [];

    protected onLoad(): void {
        GameManager.Instance.onStateChange.on(this.onStateChangeCallback, this);


        console.log(this.levelData);
        // this.setupData();
    }

    private onStateChangeCallback(state: GameState): void {
        if (state === GameState.InitData) {
            this.setupData();
        }
    }
    
    private setupData(): void {
        if (this.data && this.data.json) {
            const rawLevels = this.data.json as any[];
            this.levelData = rawLevels.map(rawLevel => {
                const levelConfig = new LevelDataConfig();

                if (rawLevel.ColorShapes && Array.isArray(rawLevel.ColorShapes)) {
                    levelConfig.ColorShapes = rawLevel.ColorShapes.map((rawColor: any) => {
                        if (rawColor && typeof rawColor._val === 'number') {
                            const val = rawColor._val;
                            const r = val & 0xff;
                            const g = (val >> 8) & 0xff;
                            const b = (val >> 16) & 0xff;
                            const a = (val >> 24) & 0xff;
                            return new Color(r, g, b, a);
                        } else if (rawColor && typeof rawColor.r === 'number') { // Fallback if r,g,b,a are directly provided
                            return new Color(rawColor.r, rawColor.g, rawColor.b, rawColor.a);
                        }
                        return new Color(); // Default or error color
                    });
                }

                if (rawLevel.ObjectDatas && Array.isArray(rawLevel.ObjectDatas)) {
                    levelConfig.ObjectDatas = rawLevel.ObjectDatas.map((rawObjData: any) => {
                        const objData = new ObjectDataConfig();
                        objData.Type = rawObjData.Type;
                        if (rawObjData.Position) {
                            objData.Position = new Vec3(rawObjData.Position.x, rawObjData.Position.y, rawObjData.Position.z);
                        }
                        if (rawObjData.Rotation) {
                            objData.Rotation = new Vec3(rawObjData.Rotation.x, rawObjData.Rotation.y, rawObjData.Rotation.z);
                        }
                        objData.layer = rawObjData.layer;
                        objData.Colors = rawObjData.Colors; // Assuming Colors is number[] and can be directly assigned
                        return objData;
                    });
                }

                if (rawLevel.BoxDatas && Array.isArray(rawLevel.BoxDatas)) {
                    levelConfig.BoxDatas = rawLevel.BoxDatas.map((rawBoxData: any) => {
                        const boxData = new BoxDataConfig();
                        boxData.colorType = rawBoxData.colorType;
                        boxData.slot = rawBoxData.slot;
                        return boxData;
                    });
                }
                return levelConfig;
            });
            // console.log('Converted levelData:', this.levelData);
        } else {
            // console.warn("JsonAsset data is not loaded or is empty.");
            this.levelData = [];
        }
    }
}


/*

16 pastel green
9 blue
14 dark gray
18 pastel lavender
15 yellow


*/