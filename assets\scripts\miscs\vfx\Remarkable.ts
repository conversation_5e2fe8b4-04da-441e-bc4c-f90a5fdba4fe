import { _decorator, Component, Node, ParticleSystem2D, Sprite, SpriteFrame, tween, UIOpacity, v2, v3 } from 'cc';
import { VFX } from './VFX';
const { ccclass, property } = _decorator;

@ccclass('Remarkable')
export class Remarkable extends VFX {
    play(){
        return new Promise(resolve=>{
            const bg = this.node.getChildByName("bg").getComponent(UIOpacity);
            bg.opacity = 0;
            tween(bg).to(0.5, {opacity:255}).call(()=>{
                this.node.getChildByName("particle1").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle2").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle3").getComponent(ParticleSystem2D).resetSystem();
                this.node.getChildByName("particle4").getComponent(ParticleSystem2D).resetSystem();
            }).start();
    
            const txt = this.node.getChildByName("txt");
            for(let i = 0; i < txt.children.length; i++){
                const char = txt.children[i];
                char.scale = v3(0,0,0);
                tween(char).delay(0.5+0.1*i).to(0.5, {scale:v3(1.4, 1.4, 1)}).to(0.2, {scale:v3(1,1,1)}).start();
            }
    
            const glow = this.node.getChildByName("glow").getComponent(UIOpacity);
            glow.opacity = 0;
            const t= txt.children.length*0.1+1.1;
            glow.node.position = txt.position;
            tween(glow).delay(t).to(0.3, {opacity:255}).delay(0.3).to(0.3, {opacity:0}).start();
            tween(glow.node).delay(t+0.15).by(0.4, {position:v3(15, 10)}).by(0.4, {position:v3(-10,-10)}).call(resolve).start();
        })
    }
}


